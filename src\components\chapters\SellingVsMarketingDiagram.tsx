import { Card, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";
import React from "react";

const FlowDiagramStep = ({ title, subtitle, className }: { title: string; subtitle: string; className?: string }) => (
  <div className="flex-1 text-center px-2">
    <p className={`font-bold text-sm md:text-base ${className}`}>{title}</p>
    <p className="text-xs md:text-sm text-text-gray">{subtitle}</p>
  </div>
);

const FlowDiagramArrow = ({ children, className }: { children: React.ReactNode; className?: string }) => (
  <div className={`flex items-center justify-between w-full p-4 rounded-lg border ${className}`}>
    {children}
  </div>
);

export const SellingVsMarketingDiagram = () => {
  const sellingSteps = [
    { title: "المصنع", subtitle: "نقطة البدء" },
    { title: "المنتجات", subtitle: "التركيز" },
    { title: "البيع والترويج", subtitle: "الوسيلة" },
    { title: "الأرباح", subtitle: "من حجم المبيعات" },
  ];

  const marketingSteps = [
    { title: "السوق المستهدف", subtitle: "نقطة البدء" },
    { title: "حاجات المستهلك", subtitle: "التركيز" },
    { title: "التسويق المتكامل", subtitle: "الوسيلة" },
    { title: "الأرباح", subtitle: "من رضا المستهلك" },
  ];

  return (
    <Card className="mt-8 border-t pt-6">
      <CardHeader className="pt-0">
        <CardTitle className="text-dark-gray">مقارنة بين التوجه البيعي والتوجه التسويقي</CardTitle>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Selling Concept Flow */}
        <div>
          <h3 className="font-bold text-lg mb-3 text-dark-gray">أ. التوجه البيعي</h3>
          <FlowDiagramArrow className="bg-yellow-50 border-yellow-200">
            {sellingSteps.map((step, index) => (
              <React.Fragment key={index}>
                <FlowDiagramStep title={step.title} subtitle={step.subtitle} className="text-yellow-900" />
                {index < sellingSteps.length - 1 && (
                  <ArrowRight className="h-5 w-5 text-yellow-500 mx-1 md:mx-4 flex-shrink-0" />
                )}
              </React.Fragment>
            ))}
          </FlowDiagramArrow>
        </div>

        {/* Marketing Concept Flow */}
        <div>
          <h3 className="font-bold text-lg mb-3 text-dark-gray">ب. التوجه التسويقي</h3>
          <FlowDiagramArrow className="bg-green-50 border-green-200">
            {marketingSteps.map((step, index) => (
              <React.Fragment key={index}>
                <FlowDiagramStep title={step.title} subtitle={step.subtitle} className="text-green-900" />
                {index < marketingSteps.length - 1 && (
                  <ArrowRight className="h-5 w-5 text-green-500 mx-1 md:mx-4 flex-shrink-0" />
                )}
              </React.Fragment>
            ))}
          </FlowDiagramArrow>
        </div>
      </CardContent>
    </Card>
  );
};
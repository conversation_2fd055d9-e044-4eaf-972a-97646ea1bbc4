import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { PackagePlus, Lightbulb, ArrowLeft } from 'lucide-react';

// Re-using components from Chapter9ProductManagement for consistency
const NewProductType = ({ number, title, enTitle, children }: { number: number; title: string; enTitle: string; children: React.ReactNode }) => (
    <div className="p-4 border-r-4 border-purple-600 rounded-lg bg-purple-50/50 shadow-sm">
        <h4 className="font-bold text-purple-800 mb-2">{number}. {title} <span className="text-sm font-normal text-slate-600">({enTitle})</span></h4>
        <p className="text-slate-700 leading-relaxed">{children}</p>
    </div>
);

const ReasonItem = ({ number, children }: { number: number; children: React.ReactNode }) => (
    <li className="flex items-start gap-3">
        <span className="flex-shrink-0 h-6 w-6 bg-blue-100 text-blue-700 font-bold rounded-full flex items-center justify-center text-sm mt-0.5">
            {number}
        </span>
        <p className="text-slate-700 leading-relaxed">{children}</p>
    </li>
);

const StepBox = ({ number, title, children }: { number: number; title: string; children: React.ReactNode }) => (
    <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
        <h5 className="font-bold text-lg text-slate-800 flex items-center gap-3">
            <span className="flex-shrink-0 h-8 w-8 bg-blue-100 text-blue-700 font-bold rounded-full flex items-center justify-center">
                {number}
            </span>
            <span>{title}</span>
        </h5>
        <p className="text-slate-600 mt-2 leading-relaxed pr-11">{children}</p>
    </div>
);

const ProcessStepDiagram = ({ title, source }: { title: string; source?: string }) => (
    <div dir="rtl" className="my-8 p-4 sm:p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-lg sm:text-xl mb-4 text-slate-800">{title}</h4>
        <div className="flex flex-col md:flex-row-reverse gap-4 items-stretch">
            <div className="flex-1 bg-blue-100 border-blue-300 rounded-lg p-3 text-center font-semibold text-blue-800 shadow-sm flex items-center justify-center">
                توليد الأفكار
            </div>
            <ArrowLeft className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0 flex-shrink-0" />
            <div className="flex-1 bg-blue-100 border-blue-300 rounded-lg p-3 text-center font-semibold text-blue-800 shadow-sm flex items-center justify-center">
                غربلة الأفكار
            </div>
            <ArrowLeft className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0 flex-shrink-0" />
            <div className="flex-1 bg-blue-100 border-blue-300 rounded-lg p-3 text-center font-semibold text-blue-800 shadow-sm flex items-center justify-center">
                تحليل الأعمال
            </div>
            <ArrowLeft className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0 flex-shrink-0" />
            <div className="flex-1 bg-blue-100 border-blue-300 rounded-lg p-3 text-center font-semibold text-blue-800 shadow-sm flex items-center justify-center">
                تطوير المنتج
            </div>
            <ArrowLeft className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0 flex-shrink-0" />
            <div className="flex-1 bg-blue-100 border-blue-300 rounded-lg p-3 text-center font-semibold text-blue-800 shadow-sm flex items-center justify-center">
                اختبار المنتج
            </div>
            <ArrowLeft className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0 flex-shrink-0" />
            <div className="flex-1 bg-blue-100 border-blue-300 rounded-lg p-3 text-center font-semibold text-blue-800 shadow-sm flex items-center justify-center">
                الاتصالات (التسويق)
            </div>
        </div>
        {source && <p className="text-center text-xs text-slate-500 mt-4">{source}</p>}
    </div>
);


export const Chapter9NewProductDevelopment = () => {
  return (
    <div className="space-y-10">
      {/* Section: المنتج الجديد (New Product) - Moved from Chapter9ProductManagement */}
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800 flex items-center gap-3">
            <PackagePlus className="h-8 w-8 text-purple-700" />
            المنتج الجديد (New Product)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            لا يمكن أن يبقى المنتج على حاله لمدة طويلة من الزمن، وخصوصاً عندما تكون هنالك سلع منافسة أو بديلة عنه. عليه فإن التطور يجب أن يدخل إلى المنتج، وسواء كان ذلك من حيث المحتوى، الشكل، اللون، السعر، الوزن، العبوة... إلخ. وتشير الإحصاءات في هذا المجال أنه يدخل في السوق الأمريكية ما يقرب من (16000) منتج جديد سنوياً في مجال تجارة البقالة والخضروات، أي بمعدل (1500) منتج جديد شهرياً، وبطبيعة الحال فإن عملية التطوير هذه لا تأتي اعتباطاً بل هي نتيجة دراسة وتدقيق للعديد من المواقف والتحليلات لنتائج عمليات البيع والتي تستلزم أن تتم عملية التطوير وسواء كان ذلك لاستمرارية عملية البيع أو لمواكبة التطورات الحاصلة في مجال إنتاج تلك السلع فضلاً عن مدى إسهام هذه المنتجات الجديدة في تحقيق الأرباح المحتملة. ففي دراسة أجريت على (700) شركة أمريكية أشارت 31% من هذه الشركات بأن أرباحها المتوقعة للسنوات الخمسة القادمة ستتكون من خلال المنتجات الجديدة التي ستطرحها في السوق.
          </p>
          <p className="font-semibold pt-4 border-t">
            ولكن من المناسب الإشارة هنا إلى ما هي ماهية المنتج الجديد؟ حيث يمكن أن يأخذ الأشكال التالية وهي:
          </p>
          <div className="space-y-4">
            <NewProductType number={1} title="المنتج المخترع" enTitle="Original Product">
              وهو ذلك المنتج الجديد والذي لم يكن موجود في السوق أصلاً، أي أنه مخترع أو مكتشف وجوده لأول مرة. كما هو مثلاً (وككل حسب زمن إنتاجه) الفيديو، خدمات الشخص، الطائرة، السينما المجسمة، الستلايت (محطات البث الفضائية)، الفرن الذري... إلخ.
            </NewProductType>
            <NewProductType number={2} title="المنتج المطور" enTitle="Improved Product">
              وهي تلك السلع القديمة والتي أجريت عليها تعديلات أو تطوير لمواكبة حاجات المشتري، وكما هو مثلاً باستخدام السخان الكهربائي كتطوير للسخان الغازي أو النفطي أو التلفزيون الملون بدلاً من التلفزيون العادي (أسود وأبيض)... إلخ.
            </NewProductType>
            <NewProductType number={3} title="المنتج المعدل" enTitle="Modified Product">
              وهي تلك المنتجات القديمة التي أجريت عليها تعديلات جذرية تتوافق مع الحالات الجديدة لدى المشتري. كما هو مثلاً في تغيير أجزاء من تركيبة السيارة بما يتوافق مع السرعة المطلوبة أو في سعة عدد الركاب أو في التصميم الجديد لها. والمنتج المعدل هنا يختلف عن المنتج المطور من حيث سعة أو حجم التطوير الحاصل في المنتج ودرجة اختلافه عن سابقه.
            </NewProductType>
            <NewProductType number={4} title="المنتج بعلامة جديدة" enTitle="New Brand Product">
              وهو منتج قديم يمكن إدخاله بعلامة جديدة إلى السوق لأسباب كثيرة، قد تكون من بينها التخلص من التقليد، أو تغيير وجهة نظر الزبائن عن ذلك المنتج، أو الدخول في أسواق جديدة... إلخ.
            </NewProductType>
          </div>
        </CardContent>
      </Card>

      {/* Section: لماذا تجري عملية التطوير بمنتجات جديدة؟ - Moved from Chapter9ProductManagement */}
      <Card className="bg-white shadow-md border-t-4 border-blue-500">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-slate-800 flex items-center gap-3">
            <Lightbulb className="h-8 w-8 text-blue-700" />
            لماذا تجري عملية التطوير بمنتجات جديدة؟
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            ولكن التساؤل الذي يثار هنا هو لماذا تجري عملية التطوير للمنتجات، أو الدخول بمنتجات جديدة؟ الإجابة تكمن في الآتي:
          </p>
          <ul className="space-y-3 pr-4">
            <ReasonItem number={1}>
              استثمار الطاقات المتاحة وغير المستغلة في خطوط الإنتاج.
            </ReasonItem>
            <ReasonItem number={2}>
              إتاحة الفرصة أمام تجار الجملة والذين تخصصوا في بيع ذلك المنتج فقط، بزيادة مكانتهم وحصتهم في السوق، من خلال الدخول بمنتج جديد أو مطور.
            </ReasonItem>
            <ReasonItem number={3}>
              المنتج الجديد أو المطور سيضيف قوة جديدة للمنظمة في السوق الذي تعمل فيه.
            </ReasonItem>
            <ReasonItem number={4}>
              تخطيط المنظمة لغرض تحقيق الأرباح للمدى البعيد.
            </ReasonItem>
            <ReasonItem number={5}>
              اكتشاف مواد أولية جديدة أو تركيبيه أقل كلفة وأفضل أداء مما كان عليه في السابق.
            </ReasonItem>
          </ul>
        </CardContent>
      </Card>

      {/* New Section: خطوات تطوير المنتج الجديد */}
      <Card className="bg-white shadow-md border-t-4 border-purple-500">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800 flex items-center gap-3">
            <PackagePlus className="h-8 w-8 text-purple-700" />
            خطوات تطوير المنتج الجديد
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-lg leading-relaxed text-slate-700">
          <p>
            تمر عملية تطوير المنتج الجديد بعدد من الخطوات والتي قد تتعدد أو تتقلص تبعاً لحدود التداخل أو الانفصال الواضح فيما بينها.
          </p>
          <ProcessStepDiagram 
            title="شكل (9-3): خطوات تطوير المنتج الجديد"
            source="Source: Pride & Ferrell, Marketing, 2006, p.247"
          />
          <p>
            يمثل الإحداثي العمودي عدد الأفكار والتي اتفق الباحثون على أنها تصل إلى (64) فكرة لتقديم منتج جديد. أما الإحداثي الأفقي فإنه يمثل الفترة الزمنية التي تقطعها هذه الخطوات أو المراحل لتقديم المنتج إلى السوق، وقد تطول أو تتقصر هذه الفترة تبعاً إلى خصوصية المنتج المقدم ودرجة التعقيد فيه. وتتمثل خطوات تطوير المنتج بالآتي:
          </p>
          <div className="space-y-4">
            <StepBox number={1} title="توليد الأفكار (Idea Generation)">
              توليد الأفكار التي تطرحها الشركة لتقديم منتجات جديدة إلى السوق بمثابة أهداف استراتيجية تسعى لتحقيقها، وبالتالي فإنها تخضع إلى دراسات معمقة وتقييم في مختلف الجوانب في السعي لإنجاح المنتج في السوق ومن أجل ذلك فإنها تقدم العديد من الأفكار التي تدرس لغرض تقييمها واختيار الأفضل منها. وفي الغالب لا تنحصر مصادر هذه الأفكار من داخل الشركة فقط بل يمكن أن تكون هنالك مصادر أخرى مثل الوسطاء، المستهلكين، مراكز الأبحاث التسويقية، العاملون في حلقات التوزيع، الجامعات، المنافسون... الخ.
            </StepBox>
            <StepBox number={2} title="غربلة الأفكار (Idea Screening)">
              تتمثل بعملية تقييم للأفكار الفنية لأن تكون منتج جديد وعلى ضوء الإمكانات والقدرات المتاحة لدى المنظمة من قوى عاملة، مكان، قوى بيعية، موارد مالية، منافذ توزيعية، أبنية وتسهيلات الخ... ويتم تقييم الأفكار بإعطاء أوزان تقديرية لكل فكرة على ضوء متغيرات التقييم المعتمدة ويتم ترتيبها بشكل متدرج لاختيار الأفكار التي يمكن أن تحظى بالقبول واستبعاد تلك الأفكار غير الممكنة التطبيق أو المكلفة.
            </StepBox>
            <StepBox number={3} title="تحليل الأعمال (Business analysis)">
              تتمثل هذه الخطوة على تحليل وإقرار فيما إذا كانت هذه الفكرة يمكن أن تستمر بها أولا تستمر بذلك فإن المدراء يوصون بإجراء تحليل مالي دقيق وتقدير حجم المنافسة القائمة في السوق وما يتوقع من أرباح محتملة قبل الإقدام على اتخاذ قرار الاستمرار في تنفيذ الفكرة.
            </StepBox>
            <StepBox number={4} title="تطوير المنتج (Product development)">
              وهي مرحلة نقل المنتج من فكرة إلى حيز التنفيذ الابتدائي من خلال تقديم حجم الأعمال اللازمة لإنجاز الفكرة والعمليات الإنتاجية المطلوبة وما يترتب عليها من كلف.
            </StepBox>
            <StepBox number={5} title="اختبار المنتج (Product Testing)">
              وهي مجمل عمليات الاختبار النصبية على المنتج من حيث خصائصه المتمثلة بالآراء، الأمان، الملائمة، الجودة الخ والتي يمكن قياسها مختبرياً للتأكد من توافقها مع المواصفات المحددة مسبقاً. وبعيدها أيضاً اختبارات التسويق من خلال اختيار السوق المستهدف والبيئة التسويقية التي تعمل بها المنظمة وبما ينسجم مع خصوصية المنتج الجديد المقدم للتسوق.
            </StepBox>
            <StepBox number={6} title="الاتصالات (Commercialization)">
              وهي الخطوة الأخيرة في تطوير المنتج والتي تتم بعد استنفاذ عمليات الاختبار الإنتاجية والتسويقية على المنتج وإعداد البرنامج التسويقي وإجراء الاتفاقات التجارية مع الأطراف المستهدفة من موزعين ووسطاء، لإيصال المنتج إلى المستهلكين وعلى وفق ما خطط له ابتداءً.
            </StepBox>
          </div>
          <p className="pt-4 border-t mt-6">
            ومن المفيد إيراد بعض الأمثلة لعدد من المنتجات التي مرت بهذه المراحل والمدة الزمنية التي استغرقتها من تكوينها فكرة حتى وصولها إلى مرحلة الإنجاز وهي:
            <ul className="list-disc pr-6 space-y-1 mt-2">
                <li>مسخنات ذات فلتر (2) سنتان</li>
                <li>مستحضر شامبو الشعر للغسيل (3) سنوات</li>
                <li>راديو ترانزستور (6) سنوات</li>
                <li>مادة الأنسولين لمعالجة الالتهابات (8) سنوات</li>
                <li>التلفزيون الأبيض والأسود (16) سنة.</li>
            </ul>
          </p>
          <p className="mt-4">
            ولابد من الإشارة هنا إلى أنه من أصل (64) فكرة لا يتحقق النجاح سوى لفكرتين فقط يتم تحويلهما إلى منتج جديد يطرح في السوق ولكن مع ذلك فإن احتمال فشل أحدهما تكون بنسبة كبيرة ويبقى منتج واحد تتعامل به الشركة من مجال الأعمال التسويقي الذي طرحت ابتداءً، وهذا ما يعطي مؤشر إلى أهمية التخطيط الدقيق.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from 'lucide-react';

const ModelBox = ({ title, items, className }: { title: string, items: string[], className?: string }) => (
    <div className={`flex-1 p-4 border rounded-lg shadow-sm ${className}`}>
        <h4 className="font-bold text-center mb-3 pb-2 border-b">{title}</h4>
        <ul className="space-y-2 text-sm">
            {items.map((item, index) => (
                <li key={index}>{item}</li>
            ))}
        </ul>
    </div>
);

const ModelArrow = () => (
    <div className="flex-shrink-0 flex items-center justify-center mx-4">
        <ArrowLeft className="h-8 w-8 text-slate-600" />
    </div>
);

const ConsumerBehaviorModelDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-2 text-slate-800">شكل (1-7): نموذج سلوك الشراء للمستهلك</h4>
        <div className="flex flex-col md:flex-row-reverse gap-4 items-stretch">
            <ModelBox 
                title="البيئة" 
                items={[
                    "المزيج التسويقي (المنتج، السعر، المكان، الترويج)",
                    "مثيرات تسويقية غيرها (اقتصادية، تكنولوجية، اجتماعية، ثقافية)"
                ]}
                className="bg-blue-50 border-blue-200 text-blue-900"
            />
            <ModelArrow />
            <ModelBox 
                title="الصندوق الأسود للمشتري" 
                items={[
                    "خصائص المشتري",
                    "عملية قرار الشراء"
                ]}
                className="bg-slate-700 border-slate-800 text-white"
            />
            <ModelArrow />
            <ModelBox 
                title="استجابة المشتري" 
                items={[
                    "اتجاهات المشتري وتفضيلاته",
                    "سلوك المشتري: ماذا يشتري، أين، متى، وكم",
                    "الاهتمام والعلاقة بالعلامة التجارية"
                ]}
                className="bg-green-50 border-green-200 text-green-900"
            />
        </div>
    </div>
);

export const Chapter7ConsumerBehaviorModel = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-slate-800">
            نموذج سلوك المستهلك (Model Of Consumer Behaviors)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            يهدف النشاط التسويقي بشكل جوهري إلى مقابلة وإرضاء حاجات ورغبات المستهلك، وهذا الأمر لا يتحقق اعتباطاً بل يتطلب دراسة وفهم سلوك المستهلك بشكل دقيق وواضح. وذلك من خلال دراسة العوامل المؤثرة عليه في اتخاذ قرار الشراء وما يعقبه من سلوك لاحق. ولعل دراسة سلوك المستهلك من قبل منظمات الأعمال هو أمر واجب تبرره الآتي من الأسباب:
          </p>
          <ul className="list-disc pr-6 space-y-2 text-base">
            <li>معرفة المنظمة لردود أفعال المشتري يحقق لها الفائدة باتجاه تعزيز نجاحات إستراتيجيتها التسويقية، أو إجراء التعديلات المناسبة عليها وبما يمكنها من تحقيق رضا أفضل للمستهلك.</li>
            <li>تصب في إنجاح عناصر المزيج التسويقي المعتمدة في تنفيذ النشاط التسويقي، لكونها ستتوافق مع استجابة المستهلك وتعامله مع المنظمة المعنية.</li>
          </ul>
          <p className="mt-4">
            ومعرفة المنظمة بشكل دقيق للعوامل المؤثرة في سلوك المستهلك والأكثر تأثيراً في قراراته الشرائية. ومع ذلك فإن الاختلاف والتباين قائم ما بين المستهلكين تبعاً لأسباب ومواقف وحالات مختلفة، فقد يضع المستهلك سلم لتسلسل حاجاته ورغباته بطريقة مختلفة عما يضعها غيره، أو عما تفكر به المنظمة. لذلك فإن نقطة البدأ في فهم سلوك المستهلك هو في تحديد مؤثرات الاستجابة المتحققة لديه والتي يوضحها نموذج سلوك المستهلك (المشتري) في الشكل (1-7).
          </p>
        </CardContent>
      </Card>
      <ConsumerBehaviorModelDiagram />
    </div>
  );
};
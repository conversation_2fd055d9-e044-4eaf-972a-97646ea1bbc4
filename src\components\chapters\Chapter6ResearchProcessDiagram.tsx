import React from 'react';
import { ArrowLeft } from 'lucide-react';

const ProcessStep = ({ children, className }: { children: React.ReactNode, className?: string }) => (
    <div className={`flex-1 p-3 rounded-md shadow border text-center font-medium min-h-[80px] flex items-center justify-center ${className}`}>
        {children}
    </div>
);

const ProcessArrow = () => (
    <div className="flex-shrink-0 flex items-center justify-center mx-4">
        <ArrowLeft className="h-8 w-8 text-slate-600" />
    </div>
);

export const Chapter6ResearchProcessDiagram = () => {
  return (
    <div dir="rtl" className="my-8 p-4 sm:p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-lg sm:text-xl mb-2 text-slate-800">شكل (6-2): الخطوات المتعاقبة في إجراء البحث التسويقي</h4>
        <div className="flex flex-col md:flex-row-reverse gap-4 items-stretch">
            <ProcessStep className="bg-blue-100 border-blue-300 text-blue-800">تحديد المشكلة وأهداف البحث</ProcessStep>
            <ProcessArrow />
            <ProcessStep className="bg-green-100 border-green-300 text-green-800">تطوير خطة البحث وجمع للمعلومات</ProcessStep>
            <ProcessArrow />
            <ProcessStep className="bg-purple-100 border-purple-300 text-purple-800">تنفيذ خطة البحث وتحليل البيانات</ProcessStep>
            <ProcessArrow />
            <ProcessStep className="bg-orange-100 border-orange-300 text-orange-800">تقديم التقرير النهائي (عرض النتائج)</ProcessStep>
        </div>
    </div>
  );
};
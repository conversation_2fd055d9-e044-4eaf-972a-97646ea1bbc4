import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter4Flowchart } from './Chapter4Flowchart';
import { Chapter4Introduction } from './Chapter4Introduction';
import { Chapter4SegmentationConcepts } from './Chapter4SegmentationConcepts';
import { Chapter4SegmentationBases } from './Chapter4SegmentationBases';
import { Chapter4MarketTypes } from './Chapter4MarketTypes';
import { Chapter4Targeting } from './Chapter4Targeting';
import { Chapter4SegmentationRequirements } from './Chapter4SegmentationRequirements';
import { Chapter4SegmentationBenefits } from './Chapter4SegmentationBenefits';
import { Chapter4MarketingMix } from './Chapter4MarketingMix';
import { Chapter4TargetingStrategies } from './Chapter4TargetingStrategies';
import { BookOpen, Share2, Puzzle, LayoutGrid, ShoppingBag, Target, ListChecks, <PERSON>rk<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 4
const chapter4Quiz = quizzes.find(q => q.chapterId === 4);

// Define sections for Chapter 4
export const chapter4Sections = [
  { value: "item-1", title: "هيكلية الفصل", icon: Share2, component: <Chapter4Flowchart /> },
  { value: "item-2", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter4Introduction /> },
  { value: "item-3", title: "مفهوم تجزئة السوق", icon: Puzzle, component: <Chapter4SegmentationConcepts /> },
  { value: "item-4", title: "أسس تجزئة سوق المستهلك", icon: LayoutGrid, component: <Chapter4SegmentationBases /> },
  { value: "item-5", title: "متطلبات تجزئة السوق", icon: ListChecks, component: <Chapter4SegmentationRequirements /> },
  { value: "item-6", title: "الفوائد من تجزئة السوق", icon: Sparkles, component: <Chapter4SegmentationBenefits /> },
  { value: "item-7", title: "أنواع الأسواق", icon: ShoppingBag, component: <Chapter4MarketTypes /> },
  { value: "item-8", title: "السوق المستهدف", icon: Target, component: <Chapter4Targeting /> },
  { value: "item-9", title: "المزيج التسويقي وعملية تجزئة السوق", icon: Blend, component: <Chapter4MarketingMix /> },
  { value: "item-10", title: "استراتيجيات استهداف السوق", icon: Crosshair, component: <Chapter4TargetingStrategies /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter4Quiz ? <Quiz questions={chapter4Quiz.questions} chapterId={4} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter4Props {
  sections: typeof chapter4Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter4 = ({ sections, activeSectionValue, isPresentationMode }: Chapter4Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-2xl font-bold text-secondary-blue hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-secondary-blue flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
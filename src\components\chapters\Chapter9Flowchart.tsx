import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-purple-600 border-purple-800 text-white font-bold text-lg',
    2: 'bg-purple-200 border-purple-400 text-purple-800 font-semibold',
    3: 'bg-purple-100 border-purple-300 text-purple-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter9Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-purple-800">
        هيكلية الفصل التاسع
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-sm">المنتج والخدمات والعلامات التجارية</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>ما هو المنتج؟</FlowBox>
            <VerticalArrow />
            <FlowBox level={3}>مستويات المنتج والخدمات</FlowBox>
            <VerticalArrow />
            <FlowBox level={3}>تصنيفات المنتجات والخدمات</FlowBox>
          </div>
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>مكونات مزيج المنتج</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-purple-50 rounded-md border-2 border-dashed border-purple-200">
              <FlowBox level={3}>قرارات المنتج الفردي</FlowBox>
              <FlowBox level={3}>قرارات خط الإنتاج</FlowBox>
              <FlowBox level={3}>قرارات مزيج المنتجات</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>استراتيجية العلامة التجارية</FlowBox>
            <VerticalArrow />
            <FlowBox level={3}>قيمة العلامة التجارية</FlowBox>
            <VerticalArrow />
            <FlowBox level={3}>بناء علامات تجارية قوية</FlowBox>
          </div>
        </div>

        <VerticalArrow />
        <FlowBox level={1} className="max-w-sm">تطوير المنتج الجديد</FlowBox>
        <VerticalArrow />
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex flex-col items-center space-y-2">
                <FlowBox level={2}>المنتج الجديد (مفهوم وأنواع)</FlowBox>
            </div>
            <div className="flex flex-col items-center space-y-2">
                <FlowBox level={2}>لماذا تجري عملية التطوير؟</FlowBox>
            </div>
            <div className="flex flex-col items-center space-y-2">
                <FlowBox level={2}>خطوات تطوير المنتج الجديد</FlowBox>
            </div>
        </div>

        <VerticalArrow />
        <FlowBox level={1} className="max-w-sm">دورة حياة المنتج</FlowBox>
        <VerticalArrow />
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex flex-col items-center space-y-2">
                <FlowBox level={2}>مفهوم دورة حياة المنتج</FlowBox>
            </div>
            <div className="flex flex-col items-center space-y-2">
                <FlowBox level={2}>مراحل دورة حياة المنتج</FlowBox>
            </div>
        </div>
      </div>
    </div>
  );
};
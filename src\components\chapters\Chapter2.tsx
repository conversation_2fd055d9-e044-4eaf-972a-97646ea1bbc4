import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  ClipboardList,
  BarChart3,
  DraftingCompass,
  Rocket,
  Gauge,
  ListChecks,
  Scale,
  Share2,
  BookOpen,
  TestTube2, // Added icon for quiz
} from "lucide-react";

import { MarketingManagementDefinitions } from "./MarketingManagementDefinitions";
import { MarketingManagementTasks } from "./MarketingManagementTasks";
import { MarketingAnalysis } from "./MarketingAnalysis";
import { MarketingPlanning } from "./MarketingPlanning";
import { MarketingImplementation } from "./MarketingImplementation";
import { MarketingControl } from "./MarketingControl";
import { MarketingEthics } from "./MarketingEthics";
import { Chapter2Flowchart } from "./Chapter2Flowchart";
import { Chapter2Hero } from "./Chapter2Hero";
import React from "react";
import { Quiz } from "@/components/Quiz"; // Import Quiz component
import { quizzes } from "@/data/quizzes"; // Import quizzes data

// Find the quiz for chapter 2
const chapter2Quiz = quizzes.find(q => q.chapterId === 2);

// Define sections for Chapter 2
export const chapter2Sections = [
  {
    value: "item-0",
    title: "هيكلية الفصل",
    icon: Share2,
    component: <Chapter2Flowchart />,
  },
  {
    value: "item-intro",
    title: "مقدمة الفصل",
    icon: BookOpen,
    component: <Chapter2Hero />,
  },
  {
    value: "item-1",
    title: "تعريف إدارة التسويق",
    icon: ClipboardList,
    component: <MarketingManagementDefinitions />,
  },
  {
    value: "item-2",
    title: "التحليل التسويقي (SWOT)",
    icon: BarChart3,
    component: <MarketingAnalysis />,
  },
  {
    value: "item-3",
    title: "التخطيط التسويقي",
    icon: DraftingCompass,
    component: <MarketingPlanning />,
  },
  {
    value: "item-4",
    title: "التنفيذ التسويقي",
    icon: Rocket,
    component: <MarketingImplementation />,
  },
  {
    value: "item-5",
    title: "الرقابة التسويقية",
    icon: Gauge,
    component: <MarketingControl />,
  },
  {
    value: "item-6",
    title: "واجبات ادارة التسويق",
    icon: ListChecks,
    component: <MarketingManagementTasks />,
  },
  {
    value: "item-7",
    title: "ادارة التسويق والأخلاقيات",
    icon: Scale,
    component: <MarketingEthics />,
  },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter2Quiz ? <Quiz questions={chapter2Quiz.questions} chapterId={2} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter2Props {
  sections: typeof chapter2Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter2 = ({ sections, activeSectionValue, isPresentationMode }: Chapter2Props) => {
  return (
    <div className="space-y-10">
      <Accordion
        type="single"
        collapsible
        defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-intro")} // Only set defaultValue if not in presentation mode
        value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
        className="w-full space-y-4"
      >
        {sections.map((section) => (
          <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
            <AccordionTrigger className="p-6 text-2xl font-bold text-secondary-blue hover:no-underline data-[state=open]:bg-slate-50">
              <div className="flex items-center gap-4">
                <section.icon className="h-7 w-7 text-secondary-blue flex-shrink-0" />
                <span className="text-right">{section.title}</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="p-6 pt-2 bg-white">
              {section.component}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};
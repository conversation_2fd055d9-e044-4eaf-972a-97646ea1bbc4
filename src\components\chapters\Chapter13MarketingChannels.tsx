import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ArrowRight, ArrowDown } from 'lucide-react';

const ChannelLevel = ({ level, title, description, examples, reasons, diagram }: {
    level: string;
    title: string;
    description: React.ReactNode;
    examples?: string[];
    reasons?: string[];
    diagram?: React.ReactNode;
}) => (
    <div className="p-6 bg-white rounded-lg border border-slate-200 shadow-sm space-y-4">
        <h4 className="font-bold text-xl text-indigo-800">{level}. {title}</h4>
        <p className="text-slate-700 leading-relaxed">{description}</p>
        {examples && examples.length > 0 && (
            <div>
                <h5 className="font-semibold text-slate-700 mb-2">أمثلة:</h5>
                <ul className="list-disc pr-6 space-y-1 text-slate-600">
                    {examples.map((ex, i) => <li key={i}>{ex}</li>)}
                </ul>
            </div>
        )}
        {reasons && reasons.length > 0 && (
            <div>
                <h5 className="font-semibold text-slate-700 mb-2">أسباب استخدامه:</h5>
                <ul className="list-disc pr-6 space-y-1 text-slate-600">
                    {reasons.map((reason, i) => <li key={i}>{reason}</li>)}
                </ul>
            </div>
        )}
        {diagram && <div className="mt-4">{diagram}</div>}
    </div>
);

const ConsumerChannelsDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (13-4): المنافذ التوزيعية في السلع الاستهلاكية</h4>
        <div className="flex flex-col items-center">
            <div className="grid grid-cols-4 gap-4 w-full max-w-3xl">
                {/* Column 1: Manufacturer */}
                <div className="flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">المنتج (المصنع)</div>
                </div>
                {/* Column 2: Wholesaler */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">جملة</div>
                </div>
                {/* Column 3: Retailer */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">مفرد</div>
                </div>
                {/* Column 4: Consumer */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">المستهلك</div>
                </div>
            </div>
            {/* Arrows and Labels */}
            <div className="relative w-full max-w-3xl h-40 mt-[-1rem]">
                {/* Channel 0 */}
                <div className="absolute top-0 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-0 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute top-0 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">0</span>
                <div className="absolute top-0 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-0 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />

                {/* Channel 1 */}
                <div className="absolute top-1/4 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/4 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute top-1/4 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">1</span>
                <div className="absolute top-1/4 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/4 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />

                {/* Channel 2 */}
                <div className="absolute top-1/2 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/2 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute top-1/2 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">2</span>
                <div className="absolute top-1/2 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/2 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />

                {/* Channel 3 */}
                <div className="absolute bottom-0 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute bottom-0 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute bottom-0 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">3</span>
                <div className="absolute bottom-0 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute bottom-0 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />
            </div>
        </div>
        <p className="text-center text-xs text-slate-500 mt-6">Source: Kotler & Keller, Marketing Management, 2016, p.587</p>
    </div>
);

const BusinessChannelsDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (13-5): المنافذ التوزيعية في سلع الأعمال</h4>
        <div className="flex flex-col items-center">
            <div className="grid grid-cols-4 gap-4 w-full max-w-3xl">
                {/* Column 1: Manufacturer */}
                <div className="flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">المنتج (المصنع)</div>
                </div>
                {/* Column 2: Industrial Distributor / Manufacturer's Rep */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">موزع صناعي</div>
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700 mt-2">وكيل المنتج</div>
                </div>
                {/* Column 3: Industrial Customer */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">الزبون الصناعي</div>
                </div>
                {/* Column 4: Industrial Customer (repeated for clarity of flow) */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">الزبون الصناعي</div>
                </div>
            </div>
            {/* Arrows and Labels */}
            <div className="relative w-full max-w-3xl h-40 mt-[-1rem]">
                {/* Channel 0 */}
                <div className="absolute top-0 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-0 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute top-0 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">0</span>
                <div className="absolute top-0 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-0 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />

                {/* Channel 1 */}
                <div className="absolute top-1/4 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/4 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute top-1/4 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">1</span>
                <div className="absolute top-1/4 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/4 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />

                {/* Channel 2 */}
                <div className="absolute top-1/2 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/2 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute top-1/2 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">2</span>
                <div className="absolute top-1/2 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-1/2 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />

                {/* Channel 3 */}
                <div className="absolute bottom-0 right-[calc(75%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute bottom-0 right-[calc(75%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute bottom-0 right-[calc(75%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">3</span>
                <div className="absolute bottom-0 left-[calc(25%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute bottom-0 left-[calc(25%-1.5rem)] -translate-x-1/2 text-slate-400" />
            </div>
        </div>
        <p className="text-center text-xs text-slate-500 mt-6">Source: Kotler & Keller, Marketing Management, 2016, p.587</p>
    </div>
);


export const Chapter13MarketingChannels = () => {
  return (
    <div className="space-y-10">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800">
            أنواع المنافذ التسويقية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-lg leading-relaxed text-slate-700">
          <p>
            هنالك العديد من الأشكال التي قد تأخذها المنافذ التسويقية لسلع المستهلك والتي قد تطول أو تتقصر بتعدد الأعضاء المشاركون في القناة، ولكن بعامة يمكن أن يوضح الشكل (13-4) أنواع منافذ سلع المستهلك وهي:
          </p>
        </CardContent>
      </Card>

      <ConsumerChannelsDiagram />

      <div className="space-y-8 pt-6 border-t">
        <h3 className="text-2xl font-bold text-slate-800">المنافذ التسويقية لسلع المستهلك</h3>
        <ChannelLevel
          level="1"
          title="المنفذ الصفري (المباشر) Zero – Level Channel"
          description={
            <>
              <p>
                ويسمى أيضاً بالمنفذ المباشر عندما يتم إستخدامه من قبل المنتج في بيع إنتاجه إلى المستهلك النهائي بشكل مباشر، والذي يأخذ أشكال مختلفة ومنها على سبيل المثال:
              </p>
              <ul className="list-disc pr-6 space-y-1 text-slate-600">
                  <li>البيع عن طريق طرق الأبواب (البائع الجوال أو الطواف على المنازل).</li>
                  <li>الطلب عن طريق البريد.</li>
                  <li>البيع عبر شاشات التلفزيون (دوائر بث مغلقة).</li>
                  <li>مخازن البيع التي يمتلكها المنتج (باب المصنع، متاجر خاصة به).</li>
              </ul>
              <p className="mt-4">
                ويعتبر هذا المنفذ من أقصر المنافذ التوزيعية لكونه لا يحتوي على أية حلقة وسيطة بين المنتج والمستهلك، والمدعاة أو الأسباب في إستخدامه في توزيع السلع الاستهلاكية يعود إلى الآتي:
              </p>
            </>
          }
          reasons={[
            "التخلص من أرباح الوسطاء، ولكون هامش ربح البضاعة الواحدة بسيط.",
            "قد تكون تكوين السلعة محدود.",
            "كميات الإنتاج من السلعة محدودة.",
            "النطاق الجغرافي لتوزيع البضاعة محدود."
          ]}
          examples={[
            "الحلويات والمعجنات، الخبز، المحاصيل الزراعية عند جمعها وعرضها من قبل الفلاح على الطريق العام."
          ]}
        />

        <ChannelLevel
          level="2"
          title="المنفذ الأحادي Level Channel – One"
          description="يستخدم هذا المنفذ بوجود وسيط واحد ضمن طريقة العملية التسويقية ويتمثل بتاجر التجزئة (بائع المفرد). ويستخدم هذا المنفذ عندما تكون كميات البيع المنتجة كبيرة وسريعة التلف في ذات الوقت، مما يتطلب اختصار في سلسلة التسويق لغرض إيصالها إلى المستهلك النهائي. كما هو مثلاً في تسويق المحاصيل الزراعية الحقلية من المنتج إلى بائع المفرد ومن ثم المستهلك النهائي أو عندما يكون هامش الربح فيها قليل جداً كما هو في المشروبات الغازية والصحف مثلاً."
        />

        <ChannelLevel
          level="3"
          title="المنفذ الثنائي Level Channel – Two"
          description="يعتبر هذا المنفذ من أكثر المنافذ استخداماً في توزيع السلع الاستهلاكية وخصوصاً عندما لا يستطيع المنتج وبسبب محدودية إمكانياته المالية والتسويقية بالإتصال بباعة المفرد المنتشرين في مناطق متعددة لذك يوكل عملية التوزيع إلى تاجر جملة أو أكثر، لكي يقوموا بعملية التوزيع إلى بائعي المفرد ومن ثم المستهلكين ومثل على ذلك عندما يكون المنتج الأولي البلاستيكية في العاصمة ويعتمد تاجر جملة في المحافظة ليقوم بعملية التوزيع إلى باعة المفرد هناك لإيصالها إلى المستهلكين وهكذا الأنواع الأخرى من البضائع."
        />

        <ChannelLevel
          level="4"
          title="المنفذ الثلاثي Level Channel – Three"
          description={
            <>
              <p>
                هذا المنفذ مكمل للمنفذ السابق، وذلك عندما يستخدم الوكيل كحلقة مضافة للمنفذ التوزيعي. حيث يقوم هذا الوكيل بتقديم الخدمات نيابة عن المنتج في المنطقة الجغرافية التي يكون مسؤول عنها في عملية توزيع البضاعة إلى باعة الجملة ومن ثم إلى المفرد. وخصوصاً عندما تحتاج هذه البضاعة إلى خدمات تسويقية كالإعلان والترويج وكما في مثالنا السابق عندما يكون هناك وكيل للمنتج في المحافظة ويعمل تاجر الجملة هناك لتوزيع البضاعة. وقد تكون البضاعة هي خزائنة وامتلاكها لا يمكنها. وبنفس الوقت قد يكون هذا الوكيل متخصص في التعامل مع هذا المنتج فقط أو مع أكثر من منتج واحد. مثال: هذا المنفذ هناك منتج آخر يخرج. أهمية خاصة في سلع المستهلك وهذا ما يسمى بالمنفذ الإرتجاعي (العكسي) Backward Channel وهو المنفذ المتخصص في استرجاع وإعادة الكثير من السلع المستخدمة لدى المستهلك وراغب في التخلص منها وبيعها. أو في الفوارغ من العلب والقناني التي يتم استنتاج محتوياتها وبإمكان المنتج إعادة استخدامها جزءاً أو كلياً في عمليات الإنتاج اللاحقة. والأمثلة على ذلك (كما هو في مشروبات غازية، القناني الزجاجية، الأجهزة المنزلية، الأجهزة المنزلية القديمة، الأثاث، الأجهزة المنزلية... إلخ). وهذا يعاد عرضها وبيعها في محلات القديمة الأسواق أو عن طريق فارغة الطريق. وهذا المنفذ لا يقتصر على دول معينة أو مناطق محددة بل يمتد إلى جميع دول العالم.
              </p>
            </>
          }
        />
      </div>

      <Card className="bg-white shadow-none border-0 mt-10">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800">
            المنافذ التسويقية لسلع الأعمال (Business-Goods Marketing Channels)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-lg leading-relaxed text-slate-700">
          <p>
            لسلع الأعمال ميزات ومواصفات متعددة تختلف بها عن سلع المستهلك، وأهم من أبرزها هو السلوك العقلاني في الشراء فضلاً عن حجم التأثير الذي يمكن أن تخلقه الصفقة لاحقاً على مجمل النشاط الإقتصادي للمشتري. وسواء كان إيجابياً أو سلبياً. وعليه فإن البائعين في سلع الأعمال غالباً ما يعتمدون أسلوب في الإتصال والتفاوض المباشر مع الزبون. والشكل (13-5) يوضح المنافذ المعتمدة في سوق سلع الأعمال وهي:
          </p>
        </CardContent>
      </Card>

      <BusinessChannelsDiagram />

      <div className="space-y-8 pt-6 border-t">
        <ChannelLevel
          level="1"
          title="المنفذ الصفري (المباشر) Zero – Level Channel"
          description="هذا المنفذ يمثل بحقيقته عملية اتصال مباشر مابين المنتج والمستخدم الصناعي دون الحاجة إلى وجود وسيط فيما بينهما، ولعل مرد ذلك يعود إلى جملة من الأسباب ومن أبرزها:"
          reasons={[
            "المشتري الصناعي يكون بحاجة واضحة إلى خدمات إرشادية في الإستعمال أو التشغيل أو الصيانة.",
            "محدودية السوق من البضائع الصناعية المباعة.",
            "عدم تكرار صفقة الشراء خلال فترات زمنية متقاربة."
          ]}
        />

        <ChannelLevel
          level="2"
          title="المنفذ الأحادي (الغير مباشر) One – Level Channel"
          description="يستخدم هذا المنفذ عندما يكون حجم المبيعات كبير، وعدد الزبائن كبير، ويكون المنتج غير قادر على الإتصال المباشر مع جميع الزبائن، فيلجأ إلى إستخدام وسيط واحد، وهو الموزع الصناعي Industrial Distributor. ويقوم هذا الموزع بشراء البضاعة من المنتج وخزنها في مخازنه، ومن ثم بيعها إلى الزبائن الصناعيين. ويقدم هذا الموزع خدمات إضافية للزبائن الصناعيين، مثل خدمات الصيانة، وخدمات ما بعد البيع، وخدمات التوصيل، وخدمات التمويل."
        />

        <ChannelLevel
          level="3"
          title="المنفذ الثنائي (الغير مباشر) Two – Level Channel"
          description="يستخدم هذا المنفذ عندما يكون حجم المبيعات كبير جداً، وعدد الزبائن كبير جداً، ويكون المنتج غير قادر على الإتصال المباشر مع جميع الزبائن، فيلجأ إلى إستخدام وسيطين، وهما الموزع الصناعي Industrial Distributor، ووكيل المنتج Manufacturer's Representative. ويقوم وكيل المنتج بتقديم خدمات إضافية للموزع الصناعي، مثل خدمات الترويج، وخدمات البيع الشخصي، وخدمات ما بعد البيع، وخدمات التوصيل، وخدمات التمويل."
        />

        <ChannelLevel
          level="4"
          title="المنفذ الثلاثي (الغير مباشر) Three – Level Channel"
          description="يستخدم هذا المنفذ عندما يكون حجم المبيعات كبير جداً، وعدد الزبائن كبير جداً، ويكون المنتج غير قادر على الإتصال المباشر مع جميع الزبائن، فيلجأ إلى إستخدام ثلاثة وسطاء، وهم الموزع الصناعي Industrial Distributor، ووكيل المنتج Manufacturer's Representative، وتاجر الجملة Wholesaler. ويقوم تاجر الجملة بتقديم خدمات إضافية للموزع الصناعي، مثل خدمات الترويج، وخدمات البيع الشخصي، وخدمات ما بعد البيع، وخدمات التوصيل، وخدمات التمويل."
        />
      </div>
    </div>
  );
};
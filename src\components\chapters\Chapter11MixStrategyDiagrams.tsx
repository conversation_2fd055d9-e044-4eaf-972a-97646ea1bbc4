import React from 'react';
import { ArrowLeft, ArrowRight, Factory, Megaphone, Store, Users, Warehouse } from 'lucide-react';

// الشكل (11-7)
const BarSegment = ({ width, color, label }: { width: string; color: string; label: string }) => (
    <div style={{ width }} className={`h-8 flex items-center justify-center text-white text-xs font-bold transition-all duration-300 hover:brightness-110 ${color}`}>
        {label}
    </div>
);

export const RelativeImportanceDiagram = () => {
    const consumerGoods = [
        { label: "الإعلان", width: "45%", color: "bg-blue-500" },
        { label: "ترويج المبيعات", width: "25%", color: "bg-green-500" },
        { label: "البيع الشخصي", width: "20%", color: "bg-orange-500" },
        { label: "علاقات عامة", width: "10%", color: "bg-purple-500" },
    ];

    const industrialGoods = [
        { label: "البيع الشخصي", width: "45%", color: "bg-orange-500" },
        { label: "ترويج المبيعات", width: "25%", color: "bg-green-500" },
        { label: "الإعلان", width: "20%", color: "bg-blue-500" },
        { label: "علاقات عامة", width: "10%", color: "bg-purple-500" },
    ];

    return (
        <div dir="rtl" className="my-6 p-6 bg-slate-50 rounded-2xl border-2 border-slate-200 shadow-md">
            <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (11-7): الأهمية النسبية لعناصر المزيج الترويجي</h4>
            <div className="space-y-6">
                {/* Consumer Goods */}
                <div className="space-y-2">
                    <h5 className="font-bold text-slate-700">أ. السلع الاستهلاكية</h5>
                    <div className="flex w-full rounded-lg overflow-hidden shadow-inner bg-slate-200">
                        {consumerGoods.map(item => <BarSegment key={item.label} {...item} />)}
                    </div>
                </div>
                {/* Industrial Goods */}
                <div className="space-y-2">
                    <h5 className="font-bold text-slate-700">ب. السلع الصناعية</h5>
                    <div className="flex w-full rounded-lg overflow-hidden shadow-inner bg-slate-200">
                        {industrialGoods.map(item => <BarSegment key={item.label} {...item} />)}
                    </div>
                </div>
            </div>
        </div>
    );
};


// الشكل (11-9)
const PushStep = ({ icon: Icon, title }: { icon: React.ElementType, title: string }) => (
    <div className="flex flex-col items-center gap-2 text-center w-20">
        <div className="bg-green-100 text-green-700 p-3 rounded-full border-2 border-green-200">
            <Icon className="h-8 w-8" />
        </div>
        <p className="font-semibold text-slate-700 text-sm">{title}</p>
    </div>
);

const PushArrow = () => (
    <div className="flex-1 flex flex-col items-center justify-center px-2 mt-[-2rem]">
        <ArrowLeft className="h-6 w-6 text-green-500" />
        <p className="text-xs text-green-600 mt-1 font-medium">دفع / اتصالات</p>
    </div>
);

export const PushStrategyDiagram = () => (
    <div dir="rtl" className="my-6 p-6 bg-green-50/50 rounded-2xl border-2 border-green-200 shadow-md">
        <h4 className="text-center font-bold text-xl mb-6 text-green-800">شكل (11-9): إستراتيجية الدفع (Push)</h4>
        <div className="flex items-start justify-between gap-2">
            <PushStep icon={Factory} title="المنتج" />
            <PushArrow />
            <PushStep icon={Warehouse} title="تاجر الجملة" />
            <PushArrow />
            <PushStep icon={Store} title="تاجر التجزئة" />
            <PushArrow />
            <PushStep icon={Users} title="المستهلك" />
        </div>
    </div>
);


// الشكل (11-8)
const PullStep = ({ icon: Icon, title }: { icon: React.ElementType, title: string }) => (
    <div className="flex flex-col items-center gap-2 text-center w-20">
        <div className="bg-orange-100 text-orange-700 p-3 rounded-full border-2 border-orange-200">
            <Icon className="h-8 w-8" />
        </div>
        <p className="font-semibold text-slate-700 text-sm">{title}</p>
    </div>
);

const PullArrow = () => (
    <div className="flex-1 flex flex-col items-center justify-center px-2 mt-[-2rem]">
        <ArrowRight className="h-6 w-6 text-orange-500" />
        <p className="text-xs text-orange-600 mt-1 font-medium">سحب / طلب</p>
    </div>
);

export const PullStrategyDiagram = () => (
    <div dir="rtl" className="my-6 p-6 bg-orange-50/50 rounded-2xl border-2 border-orange-200 shadow-md">
        <h4 className="text-center font-bold text-xl mb-12 text-orange-800">شكل (11-8): إستراتيجية السحب (Pull)</h4>
        <div className="relative">
            {/* Top communication flow */}
            <div className="absolute -top-8 left-0 right-0 flex items-center">
                <div className="w-1/8"></div> {/* Spacer */}
                <div className="flex-grow h-8 border-t-2 border-l-2 border-r-2 border-dashed border-blue-400 rounded-t-lg"></div>
                <div className="w-1/8"></div> {/* Spacer */}
            </div>
            <div className="absolute -top-14 left-1/2 -translate-x-1/2 flex flex-col items-center gap-1">
                 <div className="flex items-center gap-2 text-blue-700">
                    <Megaphone className="h-5 w-5" />
                    <span className="font-semibold text-sm">اتصالات تسويقية</span>
                 </div>
                 <ArrowLeft className="h-6 w-6 text-blue-500" />
            </div>

            {/* Bottom demand flow */}
            <div className="flex items-start justify-between gap-2 pt-8">
                <PullStep icon={Factory} title="المنتج" />
                <PullArrow />
                <PullStep icon={Warehouse} title="تاجر الجملة" />
                <PullArrow />
                <PullStep icon={Store} title="تاجر التجزئة" />
                <PullArrow />
                <PullStep icon={Users} title="المستهلك" />
            </div>
        </div>
    </div>
);
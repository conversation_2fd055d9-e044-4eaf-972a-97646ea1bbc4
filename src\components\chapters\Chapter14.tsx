import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter14Introduction } from './Chapter14Introduction';
import { Chapter14Concept } from './Chapter14Concept';
import { Chapter14Flowchart } from './Chapter14Flowchart';
import { Chapter14Functions } from './Chapter14Functions'; // Import the new component
import { BookOpen, Share2, Truck, Settings, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 14
const chapter14Quiz = quizzes.find(q => q.chapterId === 14);

// Define sections for Chapter 14
export const chapter14Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter14Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter14Introduction /> },
  { value: "item-2", title: "مفهوم التوزيع المادي وأهميته", icon: Truck, component: <Chapter14Concept /> },
  { value: "item-3", title: "وظائف التوزيع المادي الرئيسية", icon: Settings, component: <Chapter14Functions /> }, // New section
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter14Quiz ? <Quiz questions={chapter14Quiz.questions} chapterId={14} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter14Props {
  sections: typeof chapter14Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter14 = ({ sections, activeSectionValue, isPresentationMode }: Chapter14Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-orange-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
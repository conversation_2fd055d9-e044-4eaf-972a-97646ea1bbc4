import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, Eraser, Minus, Plus, PenTool, XCircle, ArrowUpRight, Circle, Square, Star, Pointer } from 'lucide-react'; // تم تغيير HandPointing إلى Pointer
import { cn } from '@/lib/utils';

interface DrawingToolbarProps {
  currentColor: string;
  setCurrentColor: (color: string) => void;
  currentStrokeWidth: number;
  setCurrentStrokeWidth: (width: number) => void;
  isDrawingMode: boolean;
  toggleDrawingMode: () => void;
  onClearCanvas: () => void;
  currentCursorShape: string; // New prop for current cursor shape
  setCurrentCursorShape: (shape: string) => void; // New prop to set cursor shape
}

export const DrawingToolbar = ({
  currentColor,
  setCurrentColor,
  currentStrokeWidth,
  setCurrentStrokeWidth,
  isDrawingMode,
  toggleDrawingMode,
  onClearCanvas,
  currentCursorShape,
  setCurrentCursorShape,
}: DrawingToolbarProps) => {
  const colors = ['#FF0000', '#0000FF', '#000000', '#00FF00', '#FFFF00', '#FFA500']; // Red, Blue, Black, Green, Yellow, Orange
  const strokeWidths = [2, 5, 10, 15];
  const cursorShapes = [
    { name: 'pen', icon: PenTool },
    { name: 'arrow', icon: ArrowUpRight },
    { name: 'circle', icon: Circle },
    { name: 'square', icon: Square },
    { name: 'star', icon: Star },
    { name: 'pointer', icon: Pointer }, // تم تغيير hand إلى pointer
  ];

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col items-center gap-2 p-3 bg-gray-800/90 rounded-lg shadow-lg">
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleDrawingMode}
        className={cn(
          "text-white hover:bg-gray-700",
          isDrawingMode ? "bg-blue-600 hover:bg-blue-700" : ""
        )}
        title={isDrawingMode ? "إيقاف وضع الرسم" : "تفعيل وضع الرسم"}
      >
        <PenTool className="h-6 w-6" />
      </Button>

      {isDrawingMode && (
        <>
          <div className="flex flex-col gap-1">
            {colors.map((color) => (
              <Button
                key={color}
                variant="ghost"
                size="icon"
                onClick={() => setCurrentColor(color)}
                className={cn(
                  "h-8 w-8 rounded-full border-2",
                  currentColor === color ? "border-white" : "border-transparent"
                )}
                style={{ backgroundColor: color }}
                title={`اللون: ${color}`}
              />
            ))}
          </div>

          <div className="flex flex-col gap-1 mt-2">
            {strokeWidths.map((width) => (
              <Button
                key={width}
                variant="ghost"
                size="icon"
                onClick={() => setCurrentStrokeWidth(width)}
                className={cn(
                  "h-8 w-8 rounded-full flex items-center justify-center text-white hover:bg-gray-700",
                  currentStrokeWidth === width ? "bg-gray-700" : ""
                )}
                title={`حجم الخط: ${width}`}
              >
                <span style={{ width: `${width}px`, height: `${width}px`, borderRadius: '50%', backgroundColor: 'white' }} />
              </Button>
            ))}
          </div>

          <div className="flex flex-col gap-1 mt-2 border-t border-gray-700 pt-2">
            {cursorShapes.map((shape) => (
              <Button
                key={shape.name}
                variant="ghost"
                size="icon"
                onClick={() => setCurrentCursorShape(shape.name)}
                className={cn(
                  "text-white hover:bg-gray-700",
                  currentCursorShape === shape.name ? "bg-gray-700" : ""
                )}
                title={`شكل المؤشر: ${shape.name}`}
              >
                <shape.icon className="h-6 w-6" />
              </Button>
            ))}
          </div>

          <Button
            variant="ghost"
            size="icon"
            onClick={onClearCanvas}
            className="text-red-400 hover:bg-gray-700 mt-2"
            title="مسح الكل"
          >
            <Eraser className="h-6 w-6" />
          </Button>
        </>
      )}
    </div>
  );
};
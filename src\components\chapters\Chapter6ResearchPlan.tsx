import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { ArrowLeft, CheckCircle, Users, Mail, Phone, MessageSquare, Eye, FileText, Bot, User, ArrowDown } from 'lucide-react';

const PlanBox = ({ title, items, className }: { title: string, items?: string[], className?: string }) => (
    <div className={`p-3 border rounded-md shadow-sm text-center ${className}`}>
        <h5 className="font-semibold">{title}</h5>
        {items && (
            <ul className="text-sm mt-1">
                {items.map(item => <li key={item}>{item}</li>)}
            </ul>
        )}
    </div>
);

const DataCollectionDiagram = () => (
    <div dir="rtl" className="my-6 p-4 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-lg mb-4 text-dark-gray">شك<PERSON> (6-3)</h4>
        <div className="flex flex-col md:flex-row-reverse items-center justify-center gap-4">
            <PlanBox 
                title="مصادر البيانات" 
                items={["البيانات الثانوية", "البيانات الأولية"]}
                className="bg-teal-50 border-teal-300 text-teal-800"
            />
            <ArrowLeft className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
            <PlanBox 
                title="طرق جمع البيانات" 
                items={["الاتصال", "الملاحظة", "الاستبيان"]}
                className="bg-cyan-50 border-cyan-300 text-cyan-800"
            />
            <ArrowLeft className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
            <PlanBox 
                title="العينات"
                className="bg-indigo-50 border-indigo-300 text-indigo-800"
            />
        </div>
    </div>
);

const PrimaryDataMethodsDiagram = () => {
    const communicationItems = [
        { icon: Phone, text: "التلفون" },
        { icon: Users, text: "المقابلة" },
        { icon: Mail, text: "البريد" },
        { icon: Mail, text: "الإلكترونية" },
    ];

    const observationItems = [
        { icon: User, text: "الشخصية" },
        { icon: Bot, text: "الآلية" },
    ];
    
    const questionnaireItems = [
        { icon: FileText, text: "استمارة أسئلة موجهة" }
    ];

    const MethodCard = ({ icon: Icon, title, items, color }: { icon: React.ElementType, title: string, items: { icon: React.ElementType, text: string }[], color: string }) => (
        <div className={`flex-1 p-4 border rounded-lg shadow-sm ${color}`}>
            <h5 className="font-bold text-center mb-3 pb-2 border-b flex items-center justify-center gap-2">
                <Icon className="h-5 w-5" />
                {title}
            </h5>
            <ul className="space-y-2 text-sm text-right">
                {items.map((item, index) => (
                    <li key={index} className="flex items-center gap-2">
                        <item.icon className="h-4 w-4 flex-shrink-0" />
                        <span>{item.text}</span>
                    </li>
                ))}
            </ul>
        </div>
    );

    return (
        <div dir="rtl" className="my-6 p-4 bg-slate-50 rounded-xl border">
            <h4 className="text-center font-bold text-lg mb-4 text-dark-gray">شكل (6-4): طرق جمع البيانات الأولية</h4>
            <div className="flex flex-col items-center space-y-4">
                <div className="bg-blue-600 text-white rounded-lg p-3 font-bold shadow-md">
                    طرق جمع البيانات الأولية
                </div>
                
                <ArrowDown className="h-6 w-6 text-slate-400" />

                <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-4">
                    <MethodCard 
                        icon={MessageSquare} 
                        title="الاتصال" 
                        items={communicationItems}
                        color="bg-blue-50 border-blue-200 text-blue-800"
                    />
                    <MethodCard 
                        icon={Eye} 
                        title="الملاحظة" 
                        items={observationItems}
                        color="bg-green-50 border-green-200 text-green-800"
                    />
                    <MethodCard 
                        icon={FileText} 
                        title="الاستبيان" 
                        items={questionnaireItems}
                        color="bg-purple-50 border-purple-200 text-purple-800"
                    />
                </div>
            </div>
        </div>
    );
};

const SectionTitle = ({ children }: { children: React.ReactNode }) => (
    <h4 className="text-xl font-bold text-dark-gray pt-4 border-t mt-6">{children}</h4>
);

const SubSectionTitle = ({ children }: { children: React.ReactNode }) => (
    <h5 className="text-lg font-semibold text-secondary-blue mt-4">{children}</h5>
);

const MethodCardRedesigned = ({ icon: Icon, title, children, color }: { icon: React.ElementType, title: string, children: React.ReactNode, color: string }) => (
    <Card className={`bg-white shadow-md border-l-4 ${color}`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-xl text-dark-gray">
                <Icon className={`h-6 w-6 ${color.replace('border-l-', 'text-')}`} />
                {title}
            </CardTitle>
        </CardHeader>
        <CardContent className="text-text-gray leading-relaxed space-y-2 text-sm">
            {children}
        </CardContent>
    </Card>
);

export const Chapter6ResearchPlan = () => {
  return (
    <div className="space-y-4">
        <p className="text-text-gray leading-relaxed">
            تمثل خطة البحث المسار الذي يعتمده البحث للوصول الى الاهداف المطلوبة. وبالتالي فإن خطة البحث ترتبط بتحديد المشكلة والاهداف التي يسعى اليها، ولتتكامل مع المعلومات الواجب توفيرها لمتخذ القرار واقرار ما هية المعلومات ومصادرها.
        </p>
        
        <SectionTitle>جمع البيانات (Data Collection)</SectionTitle>
        <p className="text-text-gray leading-relaxed">
            تعد من أعقد الخطوات في إجراء البحث التسويقي نظراً لتعدد مفرداتها والأساليب المعتمدة في جمع البيانات وتنوع مصادرها. فضلاً عن الأخطاء المحتملة الكبيرة التي يمكن أن تحصل في عملية الجمع والتي تنعكس على النتائج النهائية. ولغرض الإحاطة في متطلبات هذه الخطوة من خطوات إجراء البحث التسويقي فإن الأمر يستوجب التطرق وبشكل موجز إلى عدد من الفقرات المكونة لهذه الخطوة وهي موضحة بالشكل (6-3).
        </p>
        <DataCollectionDiagram />

        <SubSectionTitle>أ. مراحل تنفيذ عملية جمع البيانات</SubSectionTitle>
        <p className="text-text-gray">حيث تحدد مصادر البيانات الخاصة بالبحث بشكل رئيسي بنوعين هما:</p>
        <div className="space-y-4 mt-2">
            <Card>
                <CardHeader><CardTitle className="text-dark-gray">1. البيانات الثانوية (Secondary Data)</CardTitle></CardHeader>
                <CardContent className="space-y-2">
                    <p className="text-text-gray">وهي تلك البيانات التي جمعت أو أعدت لأغراض غير أغراض البحث الذي نحن به وعلى الرغم من التشابه والتقارب في المضمون الذي يتم البحث به. ويمكن الحصول على البيانات الثانوية من مصدرين هما:</p>
                    <ul className="list-decimal pr-6 space-y-2 text-text-gray">
                        <li><strong>المصادر الداخلية:</strong> وهي تلك البيانات الموجودة في داخل المنظمة والتي قد تكون بيانات جاهزة للاستخدام وموجودة في السجلات (المبيعات، الأرباح، الرواتب، تكاليف الترويج...إلخ).</li>
                        <li><strong>المصادر الخارجية:</strong> وهي الموجودة خارج المنظمة وتتسم بتنوع مصادرها الكبير والحجم الواسع في المعلومات والحداثة فيها، والخاصة بعد استخدام الانترنت وتنوع المواقع البحثية والعملية التي يمكن الوصول إليها وبسهولة.</li>
                    </ul>
                </CardContent>
            </Card>
            <Card>
                <CardHeader><CardTitle className="text-dark-gray">2. البيانات الأولية (Primary Data)</CardTitle></CardHeader>
                <CardContent>
                    <p className="text-text-gray">وهي تلك البيانات الميدانية التي قام بتكوينها الباحث لغرض معين مرتبط بالمشكلة المبحوثة خلال مدة زمنية معينة. وتتجلى قيمة البيانات الأولية بشكل أساسي في كون المشكلة المبحوثة لا يمكن معرفة مزاياها وتأثيرها دون استطلاع الواقع الميداني لها.</p>
                </CardContent>
            </Card>
        </div>

        <SubSectionTitle>ب. طرق جمع البيانات الأولية (Data Collection Methods)</SubSectionTitle>
        <PrimaryDataMethodsDiagram />
        <div className="space-y-6 mt-4">
            <MethodCardRedesigned icon={MessageSquare} title="1. عن طريق الاتصال (Communication)" color="border-l-blue-500">
                <p>وهي المعلومات التي يقوم بجمعها الباحث بجهده الشخصي سواء كان بشكل مباشر أو غير مباشر عبر اعتماد وسائل وسيطة للوصول إلى مصدر البيانات. ومن أبرز طرق الاتصال في جمع البيانات الأولية هي:</p>
                <ul className="list-alpha pr-6 space-y-1 mt-2">
                    <li><strong>التلفون (Telephone):</strong> حيث يستخدم التلفون الثابت (الأرضي) أو المحمول (الخلوي) لجمع البيانات عبر الاتصال مع الأفراد المستهدفين من البحث وتسجيل الإجابات بشكل مباشر.</li>
                    <li><strong>المقابلات (Interviews):</strong> هو الأكثر استخداماً في بحوث التسويق والتي يمكن أن تنقسم إلى نوعين هما: المقابلات الشخصية الفردية والمقابلات الجماعية (مجموعات التركيز).</li>
                    <li><strong>البريد (Mail):</strong> يتم إرسال استمارة الاستبيان عبر البريد إلى الأفراد المستهدفين من البحث لغرض الإجابة عليها وإعادتها إلى الباحث عن طريق البريد أيضاً.</li>
                    <li><strong>المسوحات الإلكترونية (Electronic Surveys):</strong> وهي تلك الاستطلاعات التي تجرى عن طريق البريد الإلكتروني، وتتميز بسهولتها ويسرها وشيوعها بين المستجيبين ولا تحتاج إلى خبرة في الإجابات عليها.</li>
                </ul>
            </MethodCardRedesigned>
            <MethodCardRedesigned icon={Eye} title="2. الملاحظة (Observation)" color="border-l-green-500">
                <p>هي إحدى الطرق الأساسية في جمع البيانات الأولية والتي يمكن تعريفها على أنها "الطريقة المعتمدة لجمع البيانات للتعبير عن الاهتمام بالحالة المبحوثة ومراقبة الحقائق ذات الصلة بها وتسجيلها". وتنقسم إلى:</p>
                 <ul className="list-alpha pr-6 space-y-1 mt-2">
                    <li><strong>الملاحظة الشخصية (Personal observation):</strong> وهي الملاحظة المباشرة من قبل الباحث ذاته، لمراقبة تصرف حقيقي عما يحدث، ولا يتدخل في أي شيء، باتجاه التأثير على الحدث.</li>
                    <li><strong>الملاحظة الآلية (Mechanical observation):</strong> يتم الاستعانة بالأجهزة الإلكترونية بمختلف أشكالها وأنماطها للمراقبة بدلاً من الشخص ذاته لتسجيل الظاهرة.</li>
                </ul>
            </MethodCardRedesigned>
            <MethodCardRedesigned icon={FileText} title="3. الاستبيان (Questionnaire)" color="border-l-purple-500">
                <p>يرى الخبراء في مجال الدراسات والأبحاث بأن أكثر من نصف البحوث التي أجريت في مجال التسويق قد استخدمت أو استندت إلى الاستبيان كأساس في البحث.</p>
                <p>عملية جمع البيانات الأولية وهي عبارة عن استمارة تحتوي على مجموعة أسئلة متناسقة وتنصب بهدف محدد أو أكثر، تقدم إلى عينة من الأفراد لاستطلاع آرائهم وأفكارهم ومواقفهم حول موضوع الاستبيان. ويشمل الاستبيان الأركان التالية:</p>
                <ul className="list-disc pr-6 space-y-1 mt-2">
                    <li>مقدمة الاستمارة.</li>
                    <li>البيانات المميزة للمستجوب كالاسم والمهنة والعمر والدخل.</li>
                    <li>التعليمات التي يضعها الباحث للأفراد المستجوبين بكيفية الإجابة.</li>
                    <li>الأسئلة الرئيسة المتعلقة بالمشكلة.</li>
                </ul>
            </MethodCardRedesigned>
        </div>

        <SubSectionTitle>ج. العينات (Sample)</SubSectionTitle>
        <p className="text-text-gray">استخدام العينات في مجال البحث التسويقي هو أمر واجب في الدراسات والبحوث ذات العلاقة المباشرة بالمجتمع كأفراد أو مجاميع مختلفة. ويقصد بالعينة بأنها المجموعة المختارة من قبل الباحث والقادر على تمثيل المجتمع المبحوث تمثيلاً صحيحاً من حيث الخصائص والصفات.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <Card className="bg-green-50 border-green-200">
                <CardHeader><CardTitle className="flex items-center gap-2 text-dark-gray"><User className="text-green-600" /> العينات الاحتمالية (Probability Sampling)</CardTitle></CardHeader>
                <CardContent className="space-y-2 text-sm text-text-gray">
                    <p>وهي تلك العينات التي يتم اختيارها بطريقة الصدفة، ومن المحتمل أن تكون أي وحدة من وحدات المجتمع هي العينة المختارة. وبالتالي فإن جميع وحدات مجتمع البحث لها نفس الفرصة المتساوية في أن تكون هي العينة المختارة. ومن أبرز أنواعها:</p>
                    <ul className="list-alpha pr-6 space-y-1">
                        <li><strong>العينة العشوائية البسيطة:</strong> تقوم في جوهرها على أن كل مفردات المجتمع المبحوث لها نفس الفرصة في الاختيار.</li>
                        <li><strong>العينة العشوائية الطبقية:</strong> تتم على أساس تقسيم المجتمع إلى طبقات وشرائح على وفق عامل محدد.</li>
                        <li><strong>العينة المنتظمة:</strong> هي تعديل للعينة العشوائية البسيطة إذ يتم اختيار العينة على وفق اعتبارات قد تكون رقمية، مالية، زمنية.</li>
                    </ul>
                </CardContent>
            </Card>
            <Card className="bg-red-50 border-red-200">
                <CardHeader><CardTitle className="flex items-center gap-2 text-dark-gray"><Bot className="text-red-600" /> العينات غير الاحتمالية (Non-Probability Sampling)</CardTitle></CardHeader>
                <CardContent className="space-y-2 text-sm text-text-gray">
                    <p>هي تلك العينات التي يختارها الباحث بشكل مقصود أو بصورة عفوية. ويتم اعتماد هذا النوع من العينات بسبب التعقيد الحاصل في اختيار العينات الاحتمالية والانخفاض في الكلف المترتبة على عملية الاختيار قياساً بما سبق. ومن أبرز أنواعها هي:</p>
                     <ul className="list-alpha pr-6 space-y-1">
                        <li><strong>العينة الملائمة (الميسرة):</strong> هي تلك العينة التي يرى الباحث بأنه يمكن الوصول إليها بسهولة ويسر ولا يحتاج إلى جهد كبير في البحث عنها.</li>
                        <li><strong>العينة المستندة إلى حكم الباحث (العمدية):</strong> وتقوم على أساس الاختيار المتعمد للباحث إلى العينة المستهدفة والتي يستند في ذلك إلى خبرته البحثية.</li>
                        <li><strong>عينة كرة الثلج:</strong> يتم اختيار العينة الأولى عشوائياً على وفق مواصفات أو خصائص البحث، ويطلب الباحث من المستجيب بعد انتهاء المقابلة أن يوصي بمفرده أخرى يمكن أن يجري المقابلة معها.</li>
                    </ul>
                </CardContent>
            </Card>
        </div>
    </div>
  );
};
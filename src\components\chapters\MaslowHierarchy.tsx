import React from 'react';

const PyramidLevel = ({ text, color, widthClass }: { text: string, color: string, widthClass: string }) => (
    <div className={`flex items-center justify-center text-center font-semibold text-white p-2 mx-auto rounded-sm ${color} ${widthClass}`}>
        {text}
    </div>
);

export const MaslowHierarchy = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-2 text-slate-800">شكل (3-7): سلم ماسلو للحاجات</h4>
        <p className="text-center text-xs text-slate-500 mb-6">Source: <PERSON><PERSON> & Armstrong, 2018, P.171</p>
        <div className="flex flex-col-reverse space-y-reverse space-y-1 max-w-sm mx-auto">
            <PyramidLevel text="الحاجات الطبيعية" color="bg-red-600" widthClass="w-full" />
            <PyramidLevel text="حاجات الأمان" color="bg-orange-500" widthClass="w-10/12" />
            <PyramidLevel text="الحاجات الاجتماعية" color="bg-yellow-500" widthClass="w-8/12" />
            <PyramidLevel text="حاجات التقدير" color="bg-green-500" widthClass="w-6/12" />
            <PyramidLevel text="تحقيق الذات" color="bg-blue-600" widthClass="w-4/12" />
        </div>
    </div>
);
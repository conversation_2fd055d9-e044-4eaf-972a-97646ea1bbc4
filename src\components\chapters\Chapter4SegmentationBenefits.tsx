import React from 'react';

const BenefitItem = ({ number, children }: { number: number; children: React.ReactNode }) => (
    <div className="flex items-start gap-4 p-4 bg-white rounded-lg shadow-sm border">
        <div className="flex-shrink-0 h-8 w-8 bg-purple-100 text-purple-700 font-bold rounded-full flex items-center justify-center mt-1">
            {number}
        </div>
        <p className="text-text-gray leading-relaxed mt-1.5">{children}</p>
    </div>
);

export const Chapter4SegmentationBenefits = () => {
  return (
    <div className="p-6 bg-purple-50/50 border-t-4 border-purple-200 rounded-lg space-y-6">
      <h3 className="text-2xl font-bold text-dark-gray">سادساً: الفوائد من تجزئة السوق</h3>
      <p className="text-text-gray leading-relaxed">
        إن عملية تجزئة السوق لا تعتبر هدفاً بحد ذاتها، بل هي وسيلة لتحقيق مجموعة من الفوائد والمزايا التي تعود على المنظمة بالنفع وتساعدها على تحقيق أهدافها التسويقية والكلية. ومن أبرز هذه الفوائد:
      </p>
      <div className="space-y-4">
        <BenefitItem number={1}>
          تساعد تجزئة السوق على تحديد السوق بشكل أكثر دقة من حيث حاجات المستهلكين ورغباتهم.
        </BenefitItem>
        <BenefitItem number={2}>
          تساعد الإدارة على اتخاذ القرارات التسويقية بشكل أفضل.
        </BenefitItem>
        <BenefitItem number={3}>
          تساعد على تحقيق أهداف المنظمة بشكل أكثر كفاءة وفاعلية.
        </BenefitItem>
        <BenefitItem number={4}>
          تساعد على تخصيص الموارد التسويقية بشكل أفضل.
        </BenefitItem>
        <BenefitItem number={5}>
          تساعد على تقييم الفرص التسويقية والتهديدات بشكل أفضل.
        </BenefitItem>
        <BenefitItem number={6}>
          تساعد على تطوير المزيج التسويقي بشكل أفضل.
        </BenefitItem>
      </div>
    </div>
  );
};
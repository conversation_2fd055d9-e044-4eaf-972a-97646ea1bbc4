"use client";

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Check<PERSON>ircle, XCircle, RefreshCw, QrCode } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { QuizQuestion } from '@/data/quizzes';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { QRCodeSVG } from "qrcode.react";

interface QuizProps {
  questions: QuizQuestion[];
  chapterId?: number; // جعل chapterId اختياريًا
}

export const Quiz = ({ questions, chapterId }: QuizProps) => {
  const [activeQuestion, setActiveQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isAnswerChecked, setIsAnswerChecked] = useState(false);
  const [score, setScore] = useState(0);
  const [showResults, setShowResults] = useState(false);

  const quizUrl = chapterId ? `${window.location.origin}/quiz/${chapterId}` : '';

  const handleSelectAnswer = (answer: string) => {
    if (isAnswerChecked) return;
    setSelectedAnswer(answer);
  };

  const handleCheckAnswer = () => {
    if (!selectedAnswer) return;
    setIsAnswerChecked(true);
    if (selectedAnswer === questions[activeQuestion].correctAnswer) {
      setScore((prev) => prev + 1);
    }
  };

  const handleNextQuestion = () => {
    if (activeQuestion < questions.length - 1) {
      setActiveQuestion((prev) => prev + 1);
      setSelectedAnswer(null);
      setIsAnswerChecked(false);
    } else {
      setShowResults(true);
    }
  };

  const handleRestartQuiz = () => {
    setActiveQuestion(0);
    setSelectedAnswer(null);
    setIsAnswerChecked(false);
    setScore(0);
    setShowResults(false);
  };

  const progressPercentage = ((activeQuestion + 1) / questions.length) * 100;

  if (showResults) {
    const resultPercentage = (score / questions.length) * 100;
    return (
      <Card className="w-full max-w-2xl mx-auto bg-slate-50">
        <CardHeader>
          <CardTitle className="text-center text-2xl text-dark-gray">نتائج الاختبار</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-lg text-text-gray mb-4">
            لقد حصلت على <span className="font-bold text-blue-600">{score}</span> من <span className="font-bold text-blue-600">{questions.length}</span> إجابات صحيحة.
          </p>
          <div className="w-40 h-40 mx-auto rounded-full flex items-center justify-center bg-blue-100 mb-6">
            <span className="text-4xl font-bold text-blue-700">{resultPercentage.toFixed(0)}%</span>
          </div>
          <Button onClick={handleRestartQuiz} className="flex items-center gap-2 mx-auto">
            <RefreshCw className="h-4 w-4" />
            إعادة الاختبار
          </Button>
        </CardContent>
      </Card>
    );
  }

  const currentQuestion = questions[activeQuestion];

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg">
      <CardHeader>
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center gap-2">
            <CardTitle className="text-xl text-dark-gray">اختبار قصير</CardTitle>
            {chapterId && (
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" title="عرض باركود الاختبار">
                    <QrCode className="h-5 w-5 text-slate-500" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>باركود الاختبار</DialogTitle>
                    <DialogDescription>
                      يمكن للطلاب مسح هذا الباركود للوصول إلى الاختبار مباشرة على أجهزتهم.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="flex items-center justify-center p-4 bg-white rounded-lg">
                    <QRCodeSVG value={quizUrl} size={256} />
                  </div>
                  <div className="text-center text-sm text-slate-500 break-all">{quizUrl}</div>
                </DialogContent>
              </Dialog>
            )}
          </div>
          <span className="font-bold text-slate-500">{activeQuestion + 1} / {questions.length}</span>
        </div>
        <Progress value={progressPercentage} className="w-full" />
      </CardHeader>
      <CardContent>
        <h3 className="text-lg font-semibold text-text-gray mb-6 text-center">{currentQuestion.question}</h3>
        <div className="space-y-3">
          {currentQuestion.options.map((option) => {
            const isCorrect = option === currentQuestion.correctAnswer;
            const isSelected = option === selectedAnswer;
            
            return (
              <Button
                key={option}
                variant="outline"
                className={cn(
                  "w-full justify-start text-right p-4 h-auto text-base",
                  isAnswerChecked && isCorrect && "bg-green-100 border-green-400 text-green-800 hover:bg-green-200",
                  isAnswerChecked && isSelected && !isCorrect && "bg-red-100 border-red-400 text-red-800 hover:bg-red-200",
                  !isAnswerChecked && isSelected && "bg-blue-100 border-blue-400"
                )}
                onClick={() => handleSelectAnswer(option)}
                disabled={isAnswerChecked}
              >
                {isAnswerChecked && isCorrect && <CheckCircle className="ml-3 h-5 w-5 text-green-600" />}
                {isAnswerChecked && isSelected && !isCorrect && <XCircle className="ml-3 h-5 w-5 text-red-600" />}
                {option}
              </Button>
            );
          })}
        </div>
        
        {isAnswerChecked && (
          <Card className="mt-6 bg-yellow-50 border-yellow-200">
            <CardContent className="p-4">
              <p className="font-semibold text-yellow-800">شرح الإجابة:</p>
              <p className="text-yellow-900">{currentQuestion.explanation}</p>
            </CardContent>
          </Card>
        )}

        <div className="mt-6 flex justify-end">
          {isAnswerChecked ? (
            <Button onClick={handleNextQuestion}>
              {activeQuestion === questions.length - 1 ? 'عرض النتيجة' : 'السؤال التالي'}
            </Button>
          ) : (
            <Button onClick={handleCheckAnswer} disabled={!selectedAnswer}>
              تأكيد الإجابة
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
import React from 'react';
import { Card } from '@/components/ui/card';

export const MarketingCourseHero = () => {
  return (
    <Card className="w-full bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200 shadow-lg overflow-hidden mb-8">
      <div className="py-10 flex flex-col items-center justify-center gap-8">
        <div className="flex-shrink-0 flex flex-col md:flex-row items-center gap-8">
          {/* Existing Image */}
          <img 
            src="https://cdn.alweb.com/thumbs/altaswieq/article/fit710x532/%D8%A3%D9%87%D9%85-%D8%A7%D9%84%D9%85%D8%B9%D9%84%D9%88%D9%85%D8%A7%D8%AA-%D8%B9%D9%86-%D8%A7%D9%84%D8%AA%D8%B3%D9%88%D9%8A%D9%82.jpg" 
            alt="Marketing Management Concept" 
            className="h-96 rounded-lg shadow-md object-contain"
          />
          {/* New Image */}
          <img 
            src="https://www2.0zz0.com/2025/07/10/03/847829375.png" 
            alt="Digital Marketing Seminar" 
            className="h-96 rounded-lg shadow-md object-contain"
          />
        </div>
        {/* النص تم نقله خارج هذا المكون */}
      </div>
    </Card>
  );
};
import { Outlet } from "react-router-dom";
import { Sidebar } from "@/components/layout/Sidebar";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import React from "react";

// Define the context type for Outlet
interface DashboardLayoutContext {
  setCurrentChapterTitle: (title: string | null) => void;
  // isPresentationMode and togglePresentationMode are now passed as props to Header/Sidebar/Footer
  // but still available for components rendered by Outlet (like ChapterPage)
  isPresentationMode: boolean; 
  togglePresentationMode: () => void; 
}

const DashboardLayout = () => {
  const [currentChapterTitle, setCurrentChapterTitle] = React.useState<string | null>(null);
  const [isPresentationMode, setIsPresentationMode] = React.useState(false);

  const togglePresentationMode = () => {
    setIsPresentationMode(prevMode => !prevMode);
  };

  // Pass all context values to Outlet
  const outletContext: DashboardLayoutContext = React.useMemo(() => ({
    setCurrentChapterTitle,
    isPresentationMode,
    togglePresentationMode,
  }), [setCurrentChapterTitle, isPresentationMode, togglePresentationMode]);

  return (
    <div className={`flex flex-row min-h-screen bg-gray-50 font-sans ${isPresentationMode ? 'overflow-hidden' : ''}`}>
      <div className={`w-72 flex-shrink-0 border-l bg-white shadow-sm ${isPresentationMode ? 'hidden' : ''}`}>
        {/* Pass isPresentationMode as a prop */}
        <Sidebar isPresentationMode={isPresentationMode} />
      </div>
      <div className="flex-1 flex flex-col relative">
        {/* Pass isPresentationMode and togglePresentationMode as props */}
        <Header isPresentationMode={isPresentationMode} togglePresentationMode={togglePresentationMode} />
        <main className={`flex-1 p-4 sm:p-6 lg:p-8 overflow-y-auto pb-28 ${isPresentationMode ? '!p-0 !pb-0 bg-black' : ''}`}>
          <Outlet context={outletContext} />
        </main>
        {/* Pass isPresentationMode and togglePresentationMode as props to Footer as well */}
        <Footer 
          currentChapterTitle={currentChapterTitle} 
          isPresentationMode={isPresentationMode} 
          togglePresentationMode={togglePresentationMode} 
        />
      </div>
    </div>
  );
};

export default DashboardLayout;
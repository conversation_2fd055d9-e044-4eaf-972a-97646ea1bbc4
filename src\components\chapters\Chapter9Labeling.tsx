import React from 'react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ticket, Fingerprint, FileText, BadgeCheck, AlertTriangle } from 'lucide-react';

const FunctionPoint = ({ icon: Icon, title, children, color }: { icon: React.ElementType, title: string, children: React.ReactNode, color: string }) => (
    <div className={`p-4 rounded-lg border-r-4 ${color} bg-white shadow-sm`}>
        <h4 className={`font-bold text-lg mb-2 flex items-center gap-2 ${color.replace('border-', 'text-')}`}>
            <Icon className="h-5 w-5" />
            {title}
        </h4>
        <p className="text-slate-600 leading-relaxed">{children}</p>
    </div>
);

export const Chapter9Labeling = () => {
  return (
    <Card className="bg-white shadow-none border-0">
        <CardHeader>
            <CardTitle className="text-3xl font-bold text-slate-800 flex items-center gap-3">
                <Ticket className="h-8 w-8 text-purple-700" />
                الترميز والملصقات (Labeling)
            </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-lg leading-relaxed text-slate-700">
            <p>
                الملصقات هي جزء لا يتجزأ من التغليف، وتتراوح من بطاقات بسيطة مرفقة بالمنتجات إلى رسومات معقدة تشكل جزءًا من العبوة. تؤدي الملصقات عدة وظائف هامة، وقد تخضع للوائح وقوانين صارمة.
            </p>
            <div className="pt-6 border-t">
                <h3 className="text-2xl font-bold text-slate-800 mb-4">وظائف الملصقات:</h3>
                <div className="space-y-4">
                    <FunctionPoint icon={Fingerprint} title="1. تحديد الهوية" color="border-blue-500">
                        أبسط وظيفة للملصق هي تحديد المنتج أو العلامة التجارية. على سبيل المثال، ملصق بسيط على برتقالة يحمل اسم العلامة التجارية "Sunkist".
                    </FunctionPoint>
                    <FunctionPoint icon={FileText} title="2. الوصف" color="border-green-500">
                        يصف الملصق عدة أشياء حول المنتج: من صنعه، مكان صنعه، تاريخ الإنتاج، محتوياته، كيفية استخدامه بأمان، والبيانات الغذائية.
                    </FunctionPoint>
                    <FunctionPoint icon={BadgeCheck} title="3. الترويج" color="border-orange-500">
                        تلعب الملصقات دورًا ترويجيًا من خلال الرسومات الجذابة والألوان التي تدعم مكانة العلامة التجارية وتجذب انتباه العملاء.
                    </FunctionPoint>
                    <FunctionPoint icon={AlertTriangle} title="4. الامتثال القانوني" color="border-red-500">
                        في العديد من الصناعات، تفرض القوانين على الشركات وضع معلومات محددة على ملصقات منتجاتها، مثل التحذيرات الصحية على منتجات التبغ أو معلومات المكونات على المنتجات الغذائية.
                    </FunctionPoint>
                </div>
            </div>
        </CardContent>
    </Card>
  );
};
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

const ProductLevelsDiagram = () => (
    <div dir="rtl" className="my-8 flex justify-center items-center">
        <div className="relative w-80 h-80 sm:w-96 sm:h-96">
            {/* Augmented Product */}
            <div className="absolute inset-0 rounded-full bg-purple-200/80 flex items-center justify-center">
                <div className="text-center">
                    <h3 className="font-bold text-purple-800 text-lg">المنتج المعزز</h3>
                    <p className="text-xs text-purple-700">(Augmented Product)</p>
                </div>
            </div>
            {/* Actual Product */}
            <div className="absolute inset-10 sm:inset-12 rounded-full bg-purple-400/80 flex items-center justify-center">
                 <div className="text-center">
                    <h3 className="font-bold text-white text-lg">المنتج الفعلي</h3>
                    <p className="text-xs text-purple-100">(Actual Product)</p>
                </div>
            </div>
            {/* Core Customer Value */}
            <div className="absolute inset-20 sm:inset-24 rounded-full bg-purple-600 flex items-center justify-center">
                 <div className="text-center">
                    <h3 className="font-bold text-white text-lg">القيمة الجوهرية</h3>
                    <p className="text-xs text-purple-200">(Core Value)</p>
                </div>
            </div>
        </div>
    </div>
);

const LevelDescription = ({ number, title, enTitle, children }: { number: number, title: string, enTitle: string, children: React.ReactNode }) => (
    <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
        <h5 className="font-bold text-lg text-slate-800 flex items-center gap-3">
            <span className="flex-shrink-0 h-8 w-8 bg-purple-100 text-purple-700 font-bold rounded-full flex items-center justify-center">
                {number}
            </span>
            <span>{title} <span className="text-sm font-normal text-slate-500">({enTitle})</span></span>
        </h5>
        <p className="text-slate-600 mt-2 leading-relaxed pr-11">{children}</p>
    </div>
);

export const Chapter9Levels = () => {
  return (
    <div className="space-y-6">
        <Card className="bg-white shadow-none border-0">
            <CardHeader>
                <CardTitle className="text-2xl font-bold text-slate-800">مستويات الثلاث المنتج والخدمة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
                <p>
                    عند تطوير المنتجات، يفكر مخططو المنتجات في المنتجات والخدمات على ثلاثة مستويات. كل مستوى يضيف المزيد من القيمة للعميل.
                </p>
            </CardContent>
        </Card>
        
        <ProductLevelsDiagram />

        <div className="space-y-4">
            <LevelDescription number={1} title="القيمة الجوهرية للزبون" enTitle="Core Customer Value">
                هو تعبير عما يريد شراءه الفرد وما يحصل عليه من منفعة جراء شراء المنتج المقصود. والتي تتمثل بقدرة المنتج على تلافي أو معالجة المشكلة أو الحاجة التي قادت المشتري لشراء هذا المنتج دون غيره. لذلك على الشركة المنتجة أو المسوقة للمنتج أن تحدد ما هي جوهر المنتج والقيمة التي سيحققها للمشتري قبل كل شيء.
            </LevelDescription>
            <LevelDescription number={2} title="المنتج الفعلي" enTitle="Actual Product">
                يتمثل بالمنتج الحقيقي والذي يعني تطوير المنتج أو الخدمة مستقبلاً من حيث التصميم ومستوى الجودة واسم العلامة المنتج ومكانتها في السوق. وكما هو على سبيل المثال بدلاً من شراء اللوح الإلكتروني iPad الذي يتمثل بالمنتج الحقيقي فهو أساس، نظام مميز للتشغيل، الغلاف، الاستخدام، وجميعها تمثل المنتج الحقيقي والمتوافق مع قيمة الزبون.
            </LevelDescription>
            <LevelDescription number={3} title="المنتج المضاف" enTitle="Augment Product">
                يتمثل بالقيمة المضافة والمصاحبة للمنتج الفعلي وجوهر قيمة المستهلك. ولذات المثال فعند شراء iPad فإن الشركة أو البائع سيقدم الضمانات للمشتري، خدمات الصيانة، الوصول إلى الخدمات التي تقدمها شركة Apple والمتعلقة بالصور، الموسيقى، المستندات، مخزن تحميل البرامج... الخ.
            </LevelDescription>
        </div>
    </div>
  );
};
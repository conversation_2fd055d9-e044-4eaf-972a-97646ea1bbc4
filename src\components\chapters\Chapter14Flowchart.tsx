import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-orange-600 border-orange-800 text-white font-bold text-lg',
    2: 'bg-orange-200 border-orange-400 text-orange-800 font-semibold',
    3: 'bg-orange-100 border-orange-300 text-orange-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter14Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-orange-800">
        هيكلية الفصل الرابع عشر
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-sm">التوزيع المادي (اللوجستك)</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-4">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>مفهوم التوزيع المادي وأهميته</FlowBox>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>وظائف التوزيع المادي الرئيسية</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-orange-50 rounded-md border-2 border-dashed border-orange-200">
              <FlowBox level={3}>النقل</FlowBox>
              <FlowBox level={3}>التخزين</FlowBox>
              <FlowBox level={3}>إدارة المخزون</FlowBox>
              <FlowBox level={3}>معالجة الطلبات</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>القرارات الاستراتيجية</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-orange-50 rounded-md border-2 border-dashed border-orange-200">
                <FlowBox level={3}>التحديات المعاصرة</FlowBox>
                <FlowBox level={3}>دور التكنولوجيا</FlowBox>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};
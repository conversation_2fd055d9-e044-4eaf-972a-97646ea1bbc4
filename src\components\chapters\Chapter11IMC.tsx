import React from 'react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from 'lucide-react';

const Definition = ({ source, text }: { source: string, text: string }) => (
  <div className="p-4 my-4 border-r-4 border-red-600 rounded-lg bg-red-50/50 shadow-sm">
    <h4 className="font-bold text-red-800 mb-2">{source}</h4>
    <blockquote className="text-slate-700 leading-relaxed italic">
      "{text}"
    </blockquote>
  </div>
);

const IMCDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (11-1)</h4>
        <div className="flex flex-col md:flex-row-reverse items-center justify-center gap-4 relative">
            <div className="flex-1 p-4 border rounded-lg shadow-sm bg-white text-center min-w-[200px]">
                المعلومات حول الزبائن وعناصر البيئة التسويقية
            </div>
            <ArrowLeft className="h-8 w-8 text-slate-400 flex-shrink-0 transform rotate-90 md:rotate-0" />
            <div className="flex-1 p-4 border rounded-lg shadow-sm bg-white text-center min-w-[200px]">
                خطة الاتصالات التسويقية
            </div>
            <ArrowLeft className="h-8 w-8 text-slate-400 flex-shrink-0 transform rotate-90 md:rotate-0" />
            <div className="flex-1 p-4 border rounded-lg shadow-sm bg-white text-center min-w-[200px]">
                الزبائن
            </div>
            {/* Feedback Loop */}
            <div className="absolute bottom-[-40px] left-0 right-0 flex justify-center items-center md:hidden">
                <p className="text-sm text-slate-600">⟳ تغذية عكسية</p>
            </div>
            <div className="hidden md:block absolute bottom-[-40px] left-0 right-0 h-10 border-b-2 border-l-2 border-r-2 border-dashed border-slate-400 rounded-b-lg">
                <p className="absolute bottom-[-25px] left-1/2 -translate-x-1/2 bg-slate-50 px-2 text-sm text-slate-600">تغذية عكسية</p>
            </div>
        </div>
    </div>
);

const CoreConceptItem = ({ number, title, enTitle, children }: { number: number; title: string; enTitle: string; children: React.ReactNode }) => (
    <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
        <h5 className="font-bold text-lg text-slate-800 flex items-start gap-3">
            <span className="flex-shrink-0 h-8 w-8 bg-red-100 text-red-700 font-bold rounded-full flex items-center justify-center">
                {number}
            </span>
            <div>
                <span>{title}</span>
                <span className="text-sm font-normal text-slate-500 mr-2">({enTitle})</span>
            </div>
        </h5>
        <p className="text-slate-600 mt-2 leading-relaxed pr-11">{children}</p>
    </div>
);

export const Chapter11IMC = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800">
            مفهوم الاتصالات التسويقية وتعريفها
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            الاتصالات التسويقية لا تختلف من حيث الجوهر عن الاتصالات الإدارية بطبيعتها العامة، بل هي جزء منها رغم الخصوصية التي تتميز بها عن بقية أنظمة الاتصالات الأخرى في المنظمة. وهذا أمر طبيعي لاختلاف وظيفة التسويق عن بقية الوظائف الأخرى، إذ تعد هي الوظيفة الوحيدة التي يكاد يكون جل عملها واهتماماتها ينصب خارج المنظمة لأنها المسؤولة عن تصريف المنتجات إلى السوق عبر أنشطتها الترويجية، وعكس صورة المنظمة الإيجابية في المجتمع وتأمين سبل الاتصال والتواصل مع الجمهور. والشكل (11-1) يوضح جوهر هذا المضمون.
          </p>
        </CardContent>
      </Card>

      <IMCDiagram />

      <div className="space-y-4 text-slate-700 leading-relaxed">
        <h4 className="font-bold text-xl">أهمية المعلومات في الاتصالات التسويقية</h4>
        <p>
          حيث يلاحظ من الشكل بأن نجاح إدارة التسويق (المسوقون) يكمن في حصولهم على المعلومات بشكلها الكمي والنوعي عن البيئة التي تعمل بها بعامة، وزبائنها بخاصة. ويتم ذلك بطبيعة الحال عبر وسائل وأدوات مختلفة في عملية الاتصال.
        </p>
        <p>
          فعملية الاتصال التسويقي تمثل في جوهرها شبكة من المعلومات المتناقلة بين طرفي عملية التبادل (البائع والمشتري) والهادفة إلى تحقيق ما يريده كل طرف منهما. وتلعب الاتصالات التسويقية الدور الرئيسي في تحديد حجم واتساع السوق الحالية والمحتملة لعمل المنظمة. وعليه يمكن تعريف الاتصالات التسويقية.
        </p>
      </div>

      <div className="pt-6 border-t">
        <Definition 
          source="التعريف الأول"
          text="تنسيق الجهود الترويجية والتسويقية الأخرى لضمان الحصول على الحد الأعلى من المعلومات وخلق التأثير والإقناع لدى الزبائن."
        />
        <p className="text-slate-700 leading-relaxed mt-2">
          وهذا التعريف يشير بشكل واضح إلى الجانب الترويجي المركزي للاتصالات التسويقية الهادفة إلى إيجاد صيغة التواصل مع الجمهور المستهدف وإمداده بالمعلومات المناسبة عن المنتجات والأنشطة التي تقدمها. فضلاً عن سعيها الحثيث حول استقبال أكبر قدر ممكن من المعلومات من السوق لاعتمادها كأساس في صياغة إستراتيجية الاتصالات التسويقية وقراراتها المتخذة في أعقاب ذلك.
        </p>
        
        <Definition 
          source="التعريف الثاني (معاصر)"
          text="الأداة التي تستخدمها الشركة لإعلام وإقناع وتذكير المستهلكين بشكل مباشر أو غير مباشر عن المنتجات والعلامات التي تتعامل بها."
        />
        <p className="text-slate-700 leading-relaxed mt-2">
          ويتضح من هذا التعريف بأنه يركز على جوانب أساسية تتمثل في:
        </p>
        <ul className="list-disc pr-6 space-y-2 mt-2 text-slate-700">
            <li><strong className="text-red-700">إعلام (Inform) السوق:</strong> بما تقوم به الشركة من نشاطات وتقديمها لمنتجات مختلفة.</li>
            <li><strong className="text-red-700">إقناع (Persuade) المستهلك:</strong> بالعمل على إقناعه بما تقدمه من تلك السلع والخدمات والأفكار لإدامة صلة العلاقة المتبادلة بينهما.</li>
            <li><strong className="text-red-700">تذكير (Remind):</strong> لا يقف الأمر عند هذا الحد من عملية الاتصال التسويقي المتبادل، بل يمتد إلى العمل على تذكيره المستمر بتلك المنافع التي سيحصل عليها من تعامله التسويقي.</li>
        </ul>
        <p className="text-slate-700 leading-relaxed mt-4">
            وتأسيساً على ذلك فإن الاتصالات التسويقية تمثل صورة الشركة الذي تصل من خلاله إلى الجمهور لبناء علاقة معهم والوصول إلى تحقيق الولاء للعلامة التي تتعامل بها الشركة أو المنتجات التي تقدمها الشركة إجمالاً. وتستطيع الشركة ومن خلال الاتصالات التسويقية من جعل المستهلكين قادرين على مشاهدة أو سماع صوت الشركة.
        </p>
      </div>

      <div className="pt-6 border-t mt-6">
        <h3 className="text-2xl font-bold text-slate-800 mb-4">العناصر الأساسية للاتصالات التسويقية</h3>
        <p className="text-slate-700 leading-relaxed mb-6">
          لتعزيز مكانته في ذهنهم وعلى هذا الاساس فأن الاتصالات التسويقية تقوم على ثلاثة عناصر اساسية هي:
        </p>
        <div className="space-y-4">
          <CoreConceptItem number={1} title="الحوار" enTitle="Dialog">
            يقوم في جوهره على تنظيم عملية الاتصال مع المجاميع المستهدفة من الجمهور وعبر الوسائل المتاحة في الاتصال من قبل المنظمة باتجاه خلق استجابة لما تقدمه المنظمة من سلع او خدمات. وقد تكون هذه الاستجابة معبر عنها بعملية شراء او الحصول على معلومات او اي صورة من صور التفاعل الهادف للوصول الى معنى معين او نتيجة.
          </CoreConceptItem>
          <CoreConceptItem number={2} title="المكانة الذهنية" enTitle="Positioning">
            يتمثل بالتأثير الإيجابي الذي تحققه المنظمة من خلال عملية الاتصال بالآخرين، وتتحقق تلك المكانة عندما ترسخ الرسالة في ذهنية المستلم لها. وغالباً ما تسعى المنظمة إلى تفعيل هذا العنصر لكونه يمثل عملية الولاء من قبل الجمهور للمنظمة ويعطي مؤشر واضح على نجاح عملية الاتصال التسويقي المتحقق.
          </CoreConceptItem>
          <CoreConceptItem number={3} title="الاستجابة" enTitle="Response">
            وهي رد الفعل الإيجابي من مستلم الرسالة المتحققة من خلال الاتصال التسويقي لمعالجة أو حل مشكلة تسويقية كان يواجهها ووجد في هذه الرسالة الحل المناسب لها، عبر قيامه بعملية الشراء أو التفاعل معها لأي مبرر كان يراه المستلم متوافق معه.
          </CoreConceptItem>
        </div>
      </div>
    </div>
  );
};
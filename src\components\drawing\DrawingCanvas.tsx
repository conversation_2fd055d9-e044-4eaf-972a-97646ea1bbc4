import React, { useRef, useEffect, useState, useCallback } from 'react';
import { PenTool, ArrowUpRight, Circle, Square, Star, Pointer } from 'lucide-react'; // تم تغيير HandPointing إلى Pointer

interface DrawingCanvasProps {
  color: string;
  strokeWidth: number;
  isDrawingMode: boolean;
  zoomLevel: number;
  onClearRef: React.MutableRefObject<(() => void) | null>;
  currentCursorShape: string; // New prop for current cursor shape
}

export const DrawingCanvas = ({ color, strokeWidth, isDrawingMode, zoomLevel, onClearRef, currentCursorShape }: DrawingCanvasProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [lastX, setLastX] = useState(0);
  const [lastY, setLastY] = useState(0);

  const getContext = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return null;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      // Adjust for high-DPI screens
      const dpi = window.devicePixelRatio || 1;
      const displayWidth = canvas.clientWidth;
      const displayHeight = canvas.clientHeight;
      if (canvas.width !== displayWidth * dpi || canvas.height !== displayHeight * dpi) {
        canvas.width = displayWidth * dpi;
        canvas.height = displayHeight * dpi;
        ctx.scale(dpi, dpi);
      }
      ctx.lineJoin = 'round';
      ctx.lineCap = 'round';
    }
    return ctx;
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = getContext();
    if (!ctx) return;

    const clearCanvas = () => {
      ctx.clearRect(0, 0, canvas.width / (window.devicePixelRatio || 1), canvas.height / (window.devicePixelRatio || 1));
    };

    onClearRef.current = clearCanvas; // Expose clear function to parent

    // Handle canvas resize
    const resizeObserver = new ResizeObserver(() => {
      const oldImageData = ctx.getImageData(0, 0, canvas.width / (window.devicePixelRatio || 1), canvas.height / (window.devicePixelRatio || 1));
      getContext(); // Re-initialize context and scale
      ctx.putImageData(oldImageData, 0, 0); // Restore image data
    });
    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.unobserve(canvas);
      onClearRef.current = null;
    };
  }, [getContext, onClearRef]);

  const draw = useCallback((e: MouseEvent) => {
    if (!isDrawing || !isDrawingMode) return;

    const ctx = getContext();
    if (!ctx) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const currentX = (e.clientX - rect.left) / zoomLevel;
    const currentY = (e.clientY - rect.top) / zoomLevel;

    ctx.strokeStyle = color;
    ctx.lineWidth = strokeWidth;

    ctx.beginPath();
    ctx.moveTo(lastX, lastY);
    ctx.lineTo(currentX, currentY);
    ctx.stroke();

    setLastX(currentX);
    setLastY(currentY);
  }, [isDrawing, isDrawingMode, getContext, color, strokeWidth, lastX, lastY, zoomLevel]);

  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawingMode) return;
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (!canvas) return;
    const rect = canvas.getBoundingClientRect();
    setLastX((e.clientX - rect.left) / zoomLevel);
    setLastY((e.clientY - rect.top) / zoomLevel);
  }, [isDrawingMode, zoomLevel]);

  const handleMouseUp = useCallback(() => {
    setIsDrawing(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsDrawing(false);
  }, []);

  // Base64 encoded SVGs for different cursor shapes
  const cursorMap: { [key: string]: string } = {
    'pen': `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 3a2.85 2.85 0 0 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z'/%3E%3C/svg%3E") 0 24, auto`,
    'arrow': `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m4 4 7.07 17.07 2.51-7.65 7.65-2.51L4 4z'/%3E%3C/svg%3E") 0 0, auto`,
    'circle': `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3C/svg%3E") 12 12, auto`,
    'square': `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='18' height='18' x='3' y='3' rx='2'/%3E%3C/svg%3E") 12 12, auto`,
    'star': `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2'/%3E%3C/svg%3E") 12 12, auto`,
    'pointer': `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 12v2a4 4 0 0 0 4 4h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.9 3H4a2 4 0 0 0-2 2v7Z'/%3E%3Cpath d='M18 18v2a2 2 0 0 1-2 2h-2'/%3E%3C/svg%3E") 0 0, auto`, // تم تغيير hand إلى pointer
  };

  return (
    <canvas
      ref={canvasRef}
      onMouseDown={handleMouseDown}
      onMouseMove={draw}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      className={`absolute top-0 left-0 w-full h-full z-40`}
      style={{ 
        pointerEvents: isDrawingMode ? 'auto' : 'none',
        cursor: isDrawingMode ? cursorMap[currentCursorShape] : 'default' // Apply custom cursor based on selected shape
      }}
    />
  );
};
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { ArrowLeft } from 'lucide-react';

// Diagram for Undifferentiated Strategy
const UndifferentiatedDiagram = () => (
    <div className="flex items-center justify-center gap-4 p-4 bg-slate-100 rounded-lg border">
        <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-text-gray w-32">
            السوق
        </div>
        <ArrowLeft className="h-6 w-6 text-slate-500" />
        <div className="bg-blue-100 border-blue-300 text-blue-800 p-3 rounded-md shadow text-center font-medium w-32">
            المزيج التسويقي للشركة
        </div>
    </div>
);

// Diagram for Differentiated Strategy
const DifferentiatedDiagram = () => (
    <div className="flex items-center justify-center gap-8 p-4 bg-slate-100 rounded-lg border">
        <div className="flex flex-col gap-2">
            <div className="bg-white p-2 rounded-md shadow border text-center text-sm font-medium text-text-gray">الجزء 1</div>
            <div className="bg-white p-2 rounded-md shadow border text-center text-sm font-medium text-text-gray">الجزء 2</div>
            <div className="bg-white p-2 rounded-md shadow border text-center text-sm font-medium text-text-gray">الجزء 3</div>
        </div>
        <div className="flex flex-col gap-6">
            <ArrowLeft className="h-5 w-5 text-slate-500" />
            <ArrowLeft className="h-5 w-5 text-slate-500" />
            <ArrowLeft className="h-5 w-5 text-slate-500" />
        </div>
        <div className="flex flex-col gap-2">
            <div className="bg-green-100 border-green-300 text-green-800 p-2 rounded-md shadow text-center text-sm font-medium">المزيج التسويقي 1</div>
            <div className="bg-green-100 border-green-300 text-green-800 p-2 rounded-md shadow text-center text-sm font-medium">المزيج التسويقي 2</div>
            <div className="bg-green-100 border-green-300 text-green-800 p-2 rounded-md shadow text-center text-sm font-medium">المزيج التسويقي 3</div>
        </div>
    </div>
);

// Diagram for Concentrated Strategy
const ConcentratedDiagram = () => (
    <div className="flex items-center justify-center gap-4 p-4 bg-slate-100 rounded-lg border">
        <div className="bg-white p-2 rounded-md shadow border border-slate-300">
            <div className="p-2 text-center text-sm text-text-gray">الجزء 1</div>
            <div className="p-2 text-center text-sm bg-purple-600 text-white rounded-md">الجزء 2</div>
            <div className="p-2 text-center text-sm text-text-gray">الجزء 3</div>
        </div>
        <ArrowLeft className="h-6 w-6 text-slate-500" />
        <div className="bg-purple-100 border-purple-300 text-purple-800 p-3 rounded-md shadow text-center font-medium w-32">
            المزيج التسويقي للشركة
        </div>
    </div>
);


const StrategyCard = ({ title, enTitle, diagram, children }: { title: string; enTitle: string; diagram: React.ReactNode; children: React.ReactNode }) => (
    <Card className="overflow-hidden">
        <CardHeader className="bg-slate-50 border-b">
            <CardTitle className="text-xl text-dark-gray">{title}</CardTitle>
            <p className="text-sm text-slate-500">{enTitle}</p>
        </CardHeader>
        <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row-reverse gap-6 items-center">
                <div className="lg:w-1/2 flex justify-center">{diagram}</div>
                <div className="lg:w-1/2 text-text-gray leading-relaxed">{children}</div>
            </div>
        </CardContent>
    </Card>
);

export const Chapter4TargetingStrategies = () => {
  return (
    <div className="space-y-8">
        <h3 className="text-2xl font-bold text-dark-gray text-center">استراتيجيات المزيج التسويقي والسوق المستهدف</h3>
        <p className="text-center text-text-gray -mt-6">بعد تقييم الأجزاء المختلفة، يجب على الشركة أن تقرر أي الأجزاء وكم عدد الأجزاء التي ستخدمها.</p>
        
        <StrategyCard
            title="أولاً: إستراتيجية التسويق غير المتمايزة"
            enTitle="Undifferentiated Marketing"
            diagram={<UndifferentiatedDiagram />}
        >
            <p>
                باستخدام هذه الاستراتيجية، قد تقرر الشركة تجاهل اختلافات أجزاء السوق والذهاب وراء السوق كله بعرض واحد. هذا يعني أن الشركة تصمم منتجاً ومزيجاً تسويقياً يجذب أكبر عدد من المشترين.
            </p>
        </StrategyCard>

        <StrategyCard
            title="ثانياً: إستراتيجية التسويق المتمايزة"
            enTitle="Differentiated Marketing"
            diagram={<DifferentiatedDiagram />}
        >
            <p>
                باستخدام هذه الاستراتيجية، تقرر الشركة استهداف عدة أجزاء من السوق وتصمم عروضاً منفصلة لكل منها. من خلال تقديم منتجات متنوعة، تأمل الشركة في تحقيق مبيعات أعلى ومكانة أقوى داخل كل جزء من السوق.
            </p>
        </StrategyCard>

        <StrategyCard
            title="ثالثاً: إستراتيجية التسويق المركز"
            enTitle="Concentrated Marketing"
            diagram={<ConcentratedDiagram />}
        >
            <p>
                هذه الاستراتيجية جذابة بشكل خاص عندما تكون موارد الشركة محدودة. بدلاً من السعي وراء حصة صغيرة من سوق كبير، تذهب الشركة وراء حصة كبيرة من جزء واحد أو أجزاء قليلة من السوق.
            </p>
        </StrategyCard>
    </div>
  );
};
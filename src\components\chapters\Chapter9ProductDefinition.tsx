import React from 'react';
import { Card, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Package } from 'lucide-react';

const Definition = ({ source, text }: { source: string, text: string }) => (
  <div className="p-4 border-r-4 border-purple-600 rounded-lg bg-purple-50/50 shadow-sm">
    <h4 className="font-bold text-purple-800 mb-2">{source}</h4>
    <blockquote className="text-slate-700 leading-relaxed italic">
      "{text}"
    </blockquote>
  </div>
);

export const Chapter9ProductDefinition = () => {
  return (
    <Card className="bg-white shadow-none border-0">
        <CardHeader>
            <CardTitle className="text-3xl font-bold text-slate-800 flex items-center gap-3">
                <Package className="h-8 w-8 text-purple-700" />
                تعريف المنتج
            </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
            <p className="text-base">
                بشكل عام، المنتج هو أي شيء يمكن تقديمه إلى السوق لجذب الانتباه أو الحيازة أو الاستخدام أو الاستهلاك، والذي قد يشبع حاجة أو رغبة. ومع ذلك، فإن المفهوم أوسع من مجرد أشياء مادية. إليك بعض التعريفات الرئيسية:
            </p>
            <div className="space-y-4 mt-4">
                <Definition 
                    source="تعريف كوتلر وأرمسترونغ"
                    text="المنتجات تشمل أكثر من مجرد السلع الملموسة. بالمعنى الواسع، تشمل المنتجات أيضًا الخدمات، الأحداث، الأشخاص، الأماكن، المنظمات، الأفكار، أو مزيج من هذه العناصر."
                />
                <Definition 
                    source="تعريف جمعية التسويق الأمريكية (AMA)"
                    text="حزمة من السمات (الميزات والوظائف والفوائد والاستخدامات) القادرة على التبادل أو الاستخدام؛ وعادة ما تكون مزيجًا من الأشكال الملموسة وغير الملموسة."
                />
                <Definition 
                    source="من منظور القيمة"
                    text="المنتج هو في الأساس حزمة من الفوائد التي تقدم قيمة للعميل. لا يشتري العملاء المنتجات بحد ذاتها، بل يشترون الحلول والفوائد التي تقدمها هذه المنتجات."
                />
            </div>
        </CardContent>
    </Card>
  );
};
import React from 'react';
import { CheckCircle } from 'lucide-react';

const RequirementItem = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div className="p-4 bg-green-100 rounded-lg border border-green-300 shadow-sm">
        <h5 className="font-bold text-lg text-green-800 flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            {title}
        </h5>
        <p className="text-text-gray mt-2 leading-relaxed pr-7">{children}</p>
    </div>
);

export const Chapter4SegmentationRequirements = () => {
  return (
    <div className="p-6 bg-green-50/50 border-t-4 border-green-200 rounded-lg space-y-6">
      <h3 className="text-2xl font-bold text-dark-gray">خامساً: متطلبات تجزئة السوق</h3>
      <p className="text-text-gray leading-relaxed">
        لكي تكون عملية تجزئة السوق فعالة، يجب أن تتوفر في القطاعات السوقية مجموعة من الخصائص والمتطلبات الأساسية. هذه المتطلبات تضمن أن تكون الجهود التسويقية الموجهة لهذه القطاعات مجدية وقابلة للتطبيق.
      </p>
      <div className="space-y-4">
        <RequirementItem title="1. إمكانية القياس (Measurability)">
          وتعني مدى إمكانية قياس حجم السوق وقدرته الشرائية وخصائص ذلك الجزء من السوق.
        </RequirementItem>
        <RequirementItem title="2. إمكانية الوصول (Accessibility)">
          وتعني مدى إمكانية الوصول إلى ذلك الجزء من السوق وخدمته بشكل فعال.
        </RequirementItem>
        <RequirementItem title="3. الأهمية (Substantiality)">
          وتعني أن يكون ذلك الجزء من السوق كبيراً ومربحاً بما فيه الكفاية لخدمته. ويجب أن يكون الجزء هو أكبر مجموعة متجانسة ممكنة تستحق المتابعة ببرنامج تسويقي مخصص.
        </RequirementItem>
        <RequirementItem title="4. إمكانية التمييز (Differentiability)">
          وتعني أن تكون الأجزاء قابلة للتمييز من الناحية المفاهيمية وتستجيب بشكل مختلف لعناصر وبرامج المزيج التسويقي المختلفة.
        </RequirementItem>
        <RequirementItem title="5. إمكانية التنفيذ (Actionability)">
          وتعني مدى إمكانية تصميم برامج فعالة لجذب وخدمة القطاعات.
        </RequirementItem>
      </div>
    </div>
  );
};
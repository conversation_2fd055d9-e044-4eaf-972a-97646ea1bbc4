import { Settings, MonitorOff } from "lucide-react";
import { Button } from "@/components/ui/button";
// No longer need useOutletContext here as props are passed directly

interface HeaderProps {
  isPresentationMode: boolean;
  togglePresentationMode: () => void;
}

export const Header = ({ isPresentationMode, togglePresentationMode }: HeaderProps) => {
  // isPresentationMode and togglePresentationMode are now received as props
  return (
    <header className={`sticky top-0 z-10 flex items-center justify-between h-16 px-6 bg-white border-b ${isPresentationMode ? 'bg-transparent border-b-0 absolute w-full' : ''}`}>
      <h1 className={`text-2xl font-bold text-blue-800 flex items-center gap-2 ${isPresentationMode ? 'text-white' : ''}`}>
        إدارة التسويق 
      </h1>
      <div className="flex items-center gap-2">
        {isPresentationMode ? (
          <Button variant="ghost" size="icon" onClick={togglePresentationMode} className="text-white hover:text-gray-200">
            <MonitorOff className="h-5 w-5" />
          </Button>
        ) : (
          <Button variant="ghost" size="icon">
            <Settings className="h-5 w-5" />
          </Button>
        )}
      </div>
    </header>
  );
};
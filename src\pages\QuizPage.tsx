import React from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { Quiz } from '@/components/Quiz';
import { quizzes } from '@/data/quizzes';

const QuizPage = () => {
  const { chapterId } = useParams<{ chapterId: string }>();
  const numericChapterId = chapterId ? parseInt(chapterId, 10) : NaN;

  if (isNaN(numericChapterId)) {
    return <Navigate to="/not-found" />;
  }

  const quizData = quizzes.find(q => q.chapterId === numericChapterId);

  if (!quizData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 font-sans p-4">
        <div className="text-center bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold text-red-600 mb-4">خطأ</h1>
          <p className="text-lg text-slate-700">لم يتم العثور على اختبار لهذا الفصل.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4 font-sans">
      <Quiz questions={quizData.questions} />
    </div>
  );
};

export default QuizPage;
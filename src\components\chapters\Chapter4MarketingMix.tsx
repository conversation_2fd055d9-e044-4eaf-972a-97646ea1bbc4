import React from 'react';

const MixItem = ({ title, items, color }: { title: string; items: string[]; color: 'blue' | 'green' | 'purple' | 'orange'; }) => {
    const colorClasses = {
        blue: {
            bg: 'bg-blue-50',
            border: 'border-blue-200',
            title: 'text-blue-800',
        },
        green: {
            bg: 'bg-green-50',
            border: 'border-green-200',
            title: 'text-green-800',
        },
        purple: {
            bg: 'bg-purple-50',
            border: 'border-purple-200',
            title: 'text-purple-800',
        },
        orange: {
            bg: 'bg-orange-50',
            border: 'border-orange-200',
            title: 'text-orange-800',
        },
    };

    const selectedColor = colorClasses[color];

    return (
        <div className={`border rounded-lg shadow-sm p-4 w-full h-full flex flex-col ${selectedColor.bg} ${selectedColor.border}`}>
            <h4 className={`font-bold text-center text-lg mb-3 pb-2 border-b ${selectedColor.title} ${selectedColor.border}`}>{title}</h4>
            <ul className="space-y-1 text-text-gray text-right text-sm flex-grow">
                {items.map((item, index) => (
                    <li key={index}>{item}</li>
                ))}
            </ul>
        </div>
    );
};

const StrategyGrid = ({ cells, title, caption }: { cells: boolean[], title: string, caption: string }) => (
    <div className="flex flex-col items-center">
        <h5 className="font-bold text-md text-dark-gray mb-2">{title}</h5>
        <div className="grid grid-cols-3 gap-1 border p-1 bg-slate-200 rounded-md">
            <div className="col-span-3 grid grid-cols-3 gap-1 text-center font-mono text-sm text-slate-600">
                <span>M1</span><span>M2</span><span>M3</span>
            </div>
            {cells.map((isShaded, index) => (
                <div key={index} className={`w-10 h-10 border border-slate-300 ${isShaded ? 'bg-purple-500' : 'bg-white'}`}></div>
            ))}
            <div className="col-start-4 row-start-2 row-span-3 grid grid-rows-3 gap-1 text-center font-mono text-sm text-slate-600">
                <span>P1</span><span>P2</span><span>P3</span>
            </div>
        </div>
        <p className="text-center text-sm text-text-gray mt-2">{caption}</p>
    </div>
);

const StrategyDescription = ({ letter, title, children }: { letter: string, title: string, children: React.ReactNode }) => (
    <div className="mt-4">
        <h5 className="font-bold text-lg text-secondary-blue">{letter}. {title}</h5>
        <p className="text-text-gray leading-relaxed mt-1">{children}</p>
    </div>
);

export const Chapter4MarketingMix = () => {
  const strategies = {
    singleSegment: [true, false, false, false, false, false, false, false, false],
    marketSpecialization: [true, false, false, true, false, false, true, false, false],
    productSpecialization: [false, false, false, true, true, true, false, false, false],
    selectiveSpecialization: [true, false, false, false, true, false, false, false, true],
    fullCoverage: [true, true, true, true, true, true, true, true, true],
  };

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-dark-gray">المزيج التسويقي وعملية تجزئة السوق</h3>
      <p className="text-text-gray leading-relaxed">
        لا يمكن ان تعمل الشركات في السوق دون ان تعتمد ادوات تنفيذية في تحقيق ذلك العمل، وبالتالي يعد المزيج التسويقي (المنتج، السعر، التوزيع، الترويج 4Ps) هو الاداة التنفيذية للوصول الى الزبائن وتحقيق الاشباع لحاجاتهم وتلبية رغباتهم. وبطبيعة الحال إن كل عنصر من عناصر المزيج يحتوي في طياته على العديد من الانشطة التي تعمل بشكل متكامل لتحقيق ذلك العمل او الجهد المطلوب في انجاح العمل التسويقي وتحقيق استراتيجية التسويق الموضوعة.
      </p>

      <div className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-2 text-dark-gray">شكل (3-4)</h4>
        <p className="text-center text-text-gray mb-8">عناصر المزيج التسويقي الموجه نحو الزبون</p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 items-center gap-8">
            <div className="space-y-8">
                <MixItem title="السعر" items={["قائمة الاسعار", "الخصم", "السماحات", "فترة التسديد", "شروط الدفع"]} color="green" />
                <MixItem title="الترويج" items={["الاعلان", "البيع الشخصي", "ترويج المبيعات", "العلاقات العامة", "البيع المباشر"]} color="purple" />
            </div>

            <div className="flex justify-center items-center order-first md:order-none">
                <div className="bg-blue-600 text-white rounded-full h-40 w-40 flex items-center justify-center text-center font-bold shadow-lg text-xl">
                    الزبائن<br />المستهدفون
                </div>
            </div>

            <div className="space-y-8">
                <MixItem title="المنتج" items={["الجودة", "التنوع", "التصميم", "التحسين", "الخدمات", "التغليف", "العلامة"]} color="blue" />
                <MixItem title="المكان (التوزيع)" items={["القنوات", "التغطية", "المواقع", "الخزن", "النقل", "الامداد"]} color="orange" />
            </div>
        </div>
        <p className="text-center text-xs text-slate-500 mt-8">Source: Kotler & Armstrong, Principles of Marketing, 2018, p.78</p>
      </div>

      <div className="space-y-4 pt-4 border-t">
        <h4 className="font-bold text-xl text-dark-gray">شرح عناصر المزيج التسويقي:</h4>
        <p><strong className="text-secondary-blue">السعر (Price):</strong> هو كمية المبالغ التي يستوجب على الزبائن دفعها لغرض الحصول على المنتجات أو الخدمات التي يقدمها البائع. وكما هو في شركة فورد عندما تضع أسعار لكل منتجاتها وتحدد بذات الوقت شروط الدفع والخصومات المقدمة، والسماحات، توقيتات التسديد، أسعار البيع النقدي والآجل... الخ. وجميع هذه السياسات المعتمدة تخضع لحالة السوق الاقتصادية السائدة وحجم المنافسة والقيمة المستهدفة للزبون.</p>
        <p><strong className="text-secondary-blue">المكان (Place):</strong> هي مجموعة النشاطات التي تقوم بها الشركة لجعل المنتج متاح لدى الزبائن المستهدفين. وكما هو معتمد لدى شركة فورد من مجموعة كبيرة من الوكلاء المنتشرون على أرجاء العالم (في اوربا فقط لديها 7000 وكيل ومندوب عنها) بهدف إيصال منتجاتها الى الاسواق التي يتعاملون معها. فضلاً عن قيامها بعمليات دعم المنتج في تلك الاسواق وتقديم الخدمات المطلوبة والقيام بمهام عمليات البيع بكامل تفاصيلها.</p>
        <p><strong className="text-secondary-blue">الترويج (Promotion):</strong> تمثل بأنشطة الاتصالات المختلفة التي تقوم بها الشركة لأخبار زبائنها المستهدفين عن المنتجات التي تتعامل بها. وذلك من خلال الاعلانات، البيع الشخصي، العلاقات العامة، ترويج المبيعات، التسويق المباشر والاتصالات الرقمية (الالكترونية)... الخ. وعلى سبيل المثال فأن ما انفقته شركة فورد على الاعلانات في الولايات المتحدة الامريكية فقط قد بلغ ما يقرب من 2.5 مليار دولار سنوياً على المنتجات التي تتعامل بها.</p>
        <p className="text-text-gray">وبالتالي فأن الشركة لديها خيارات متعددة في التوجه للسوق الذي تريد العمل به وبما يتوافق مع قدراتها ومكامن قوتها والاستراتيجية التسويقية الموضوعة مسبقاً.</p>
      </div>

      <div className="space-y-6 pt-6 border-t">
        <h3 className="text-2xl font-bold text-dark-gray">استراتيجيات اختيار السوق والمزيج التسويقي</h3>
        <p className="text-text-gray leading-relaxed">
          وسيكون امام الشركة خمسة (5) نماذج في اختيار السوق والمزيج التسويقي الذي تعتمده عبر المنتجات التي تقدمها وكما موضحة في الشكل (4-4) وهي:
        </p>
        <div className="p-6 bg-slate-50 rounded-xl border">
            <h4 className="text-center font-bold text-xl mb-6 text-dark-gray">شكل (4-4): النماذج في اختيار السوق</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 items-start">
                <StrategyGrid cells={strategies.singleSegment} title="أ. تركيز الجزء الواحد" caption="Single-segment concentration" />
                <StrategyGrid cells={strategies.marketSpecialization} title="ب. التخصص في السوق" caption="Market specialization" />
                <StrategyGrid cells={strategies.productSpecialization} title="ج. التخصص في المنتج" caption="Product specialization" />
                <StrategyGrid cells={strategies.selectiveSpecialization} title="د. التخصص الاختياري" caption="Selective specialization" />
            </div>
            <div className="flex justify-center mt-8">
                <StrategyGrid cells={strategies.fullCoverage} title="هـ. تغطية كاملة للسوق" caption="Full market coverage" />
            </div>
        </div>
        <div className="space-y-4">
            <StrategyDescription letter="أ" title="تركيز الجزء الواحد (Single-segment concentration)">
                في هذه الحالة، تختار الشركة جزءاً واحداً من السوق وتركز كل جهودها التسويقية لخدمة هذا الجزء.
            </StrategyDescription>
            <StrategyDescription letter="ب" title="التخصص في السوق (Market specialization)">
                هنا، تركز الشركة على خدمة حاجات متعددة لمجموعة محددة من الزبائن. أي أنها تقدم مجموعة متنوعة من المنتجات لسوق واحد.
            </StrategyDescription>
            <StrategyDescription letter="ج" title="التخصص في المنتج (Product specialization)">
                تقوم الشركة بإنتاج منتج معين وتبيعه لعدة أجزاء من السوق، مع إجراء تعديلات طفيفة على المنتج ليلائم كل جزء.
            </StrategyDescription>
            <StrategyDescription letter="د" title="التخصص الاختياري (Selective specialization)">
                تختار الشركة عدداً من الأجزاء السوقية، كل جزء منها جذاب ومناسب لأهدافها، وقد يكون هناك تآزر قليل أو معدوم بين هذه الأجزاء.
            </StrategyDescription>
            <StrategyDescription letter="هـ" title="تغطية كاملة للسوق (Full market coverage)">
                في هذه الاستراتيجية، تحاول الشركة خدمة جميع مجموعات الزبائن بجميع المنتجات التي قد يحتاجونها. هذه الاستراتيجية عادة ما تتبعها الشركات الكبيرة جداً.
            </StrategyDescription>
        </div>
      </div>
    </div>
  );
};
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Truck, Warehouse, PackageCheck, ClipboardList } from 'lucide-react';

const FunctionCard = ({ icon: Icon, title, children }: { icon: React.ElementType; title: string; children: React.ReactNode }) => (
    <Card className="bg-white shadow-sm border-t-4 border-orange-500">
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-slate-800">
                <Icon className="h-8 w-8 text-orange-700" />
                {title}
            </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4 text-slate-700 leading-relaxed">
            {children}
        </CardContent>
    </Card>
);

const SubPoint = ({ children }: { children: React.ReactNode }) => (
    <li className="flex items-start gap-2">
        <span className="h-1.5 w-1.5 bg-slate-400 rounded-full mt-2 flex-shrink-0"></span>
        <span>{children}</span>
    </li>
);

export const Chapter14Functions = () => {
  return (
    <div className="space-y-8">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800">
            وظائف التوزيع المادي الرئيسية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            لتحقيق أهداف التوزيع المادي بكفاءة، تقوم الشركات بمجموعة من الوظائف الأساسية التي تضمن التدفق السلس للمنتجات عبر سلسلة التوريد. هذه الوظائف مترابطة وتتطلب تنسيقاً دقيقاً.
          </p>
        </CardContent>
      </Card>

      <div className="space-y-6 pt-6 border-t">
        <FunctionCard icon={Truck} title="1. النقل (Transportation)">
            <p>
                يُعد النقل الوظيفة الأكثر وضوحاً في التوزيع المادي، حيث يتضمن حركة المنتجات من نقطة المنشأ إلى نقطة الاستهلاك. يهدف النقل الفعال إلى إيصال المنتجات في الوقت المناسب، وبأقل تكلفة، وبأمان.
            </p>
            <h4 className="font-semibold text-slate-700 mt-4">أنواع وسائل النقل:</h4>
            <ul className="list-disc pr-6 space-y-2">
                <li>**النقل البري (Trucking):** مرن، سريع للمسافات القصيرة والمتوسطة، ومناسب لمجموعة واسعة من المنتجات.</li>
                <li>**النقل بالسكك الحديدية (Railroads):** فعال من حيث التكلفة للمسافات الطويلة والكميات الكبيرة من البضائع الثقيلة.</li>
                <li>**النقل المائي (Water Carriers):** الأقل تكلفة للكميات الكبيرة والمسافات الطويلة، ولكنه الأبطأ.</li>
                <li>**النقل الجوي (Air Carriers):** الأسرع والأكثر تكلفة، مناسب للمنتجات ذات القيمة العالية أو التي تتطلب تسليماً عاجلاً.</li>
                <li>**خطوط الأنابيب (Pipelines):** تستخدم لنقل السوائل والغازات (مثل النفط والغاز الطبيعي) بكفاءة عالية.</li>
            </ul>
            <p className="mt-4">
                يجب على الشركات اختيار وسيلة النقل المناسبة بناءً على عوامل مثل التكلفة، السرعة، الموثوقية، القدرة، وإمكانية الوصول.
            </p>
        </FunctionCard>

        <FunctionCard icon={Warehouse} title="2. التخزين (Warehousing)">
            <p>
                يتضمن التخزين الاحتفاظ بالمنتجات وتخزينها قبل بيعها أو نقلها. تلعب المستودعات دوراً حاسماً في موازنة العرض والطلب، وتوفير المنتجات عند الحاجة إليها.
            </p>
            <h4 className="font-semibold text-slate-700 mt-4">أنواع المستودعات:</h4>
            <ul className="list-disc pr-6 space-y-2">
                <li>**المستودعات الخاصة (Private Warehouses):** مملوكة ومدارة من قبل الشركة نفسها، توفر تحكماً كاملاً ولكن تتطلب استثماراً كبيراً.</li>
                <li>**المستودعات العامة (Public Warehouses):** مملوكة ومدارة من قبل شركات متخصصة، توفر مرونة وتكاليف متغيرة.</li>
                <li>**مراكز التوزيع (Distribution Centers):** مستودعات آلية مصممة لتحريك البضائع بدلاً من تخزينها لفترات طويلة، تهدف إلى تسريع تدفق المنتجات.</li>
            </ul>
            <p className="mt-4">
                تتضمن قرارات التخزين اختيار نوع المستودع، موقعه، وتصميمه لضمان الكفاءة التشغيلية.
            </p>
        </FunctionCard>

        <FunctionCard icon={PackageCheck} title="3. إدارة المخزون (Inventory Management)">
            <p>
                تتعلق إدارة المخزون بالحفاظ على المستويات المثلى للمنتجات المتاحة لتلبية طلب العملاء دون تكبد تكاليف تخزين أو تقادم مفرطة.
            </p>
            <h4 className="font-semibold text-slate-700 mt-4">أهداف إدارة المخزون:</h4>
            <ul className="list-disc pr-6 space-y-2">
                <li>**تلبية طلب العملاء:** ضمان توفر المنتجات عند الحاجة إليها.</li>
                <li>**تقليل تكاليف التخزين:** تجنب المخزون الزائد الذي يزيد من تكاليف التخزين والتأمين والتقادم.</li>
                <li>**تحسين التدفق النقدي:** ربط رأس المال في المخزون يمكن أن يؤثر على السيولة.</li>
            </ul>
            <p className="mt-4">
                تستخدم الشركات أنظمة مثل "في الوقت المناسب" (Just-in-Time - JIT) لتقليل المخزون إلى الحد الأدنى، وأنظمة تخطيط موارد المؤسسات (ERP) لتحسين إدارة المخزون.
            </p>
        </FunctionCard>

        <FunctionCard icon={ClipboardList} title="4. معالجة الطلبات (Order Processing)">
            <p>
                تتضمن معالجة الطلبات جميع الأنشطة اللازمة لاستلام طلب العميل، معالجته، وتجهيزه للشحن. تهدف هذه الوظيفة إلى ضمان الدقة والسرعة في تلبية الطلبات.
            </p>
            <h4 className="font-semibold text-slate-700 mt-4">خطوات معالجة الطلب:</h4>
            <ul className="list-disc pr-6 space-y-2">
                <SubPoint>استلام الطلب (Order Receipt).</SubPoint>
                <SubPoint>التحقق من الطلب (Order Verification).</SubPoint>
                <SubPoint>تجهيز الطلب (Order Picking and Packing).</SubPoint>
                <SubPoint>الشحن (Shipping).</SubPoint>
                <SubPoint>إصدار الفواتير (Invoicing).</SubPoint>
            </ul>
            <p className="mt-4">
                تساهم معالجة الطلبات الفعالة في تحسين رضا العملاء وتقليل الأخطاء والتأخيرات.
            </p>
        </FunctionCard>
      </div>
    </div>
  );
};
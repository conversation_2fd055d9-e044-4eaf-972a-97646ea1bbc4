import React from 'react';
import { ArrowDown } from 'lucide-react';

export const Chapter9MaturityStageDiagram = () => {
  return (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (9-6): الحالات الحاصلة في مرحلة النضج</h4>
        <div className="relative w-full h-64 flex items-end justify-center">
            {/* X-axis */}
            <div className="absolute bottom-0 left-0 w-full h-px bg-slate-400"></div>
            <span className="absolute bottom-[-25px] left-1/2 -translate-x-1/2 text-sm text-slate-600">الزمن</span>

            {/* Y-axis */}
            <div className="absolute top-0 right-0 h-full w-px bg-slate-400"></div>
            <span className="absolute top-1/2 right-[-40px] -translate-y-1/2 text-sm text-slate-600 transform -rotate-90 whitespace-nowrap">الأرباح</span>

            {/* Zero line for profits */}
            <div className="absolute bottom-1/4 left-0 w-full h-px bg-slate-300 border-dashed"></div>
            <span className="absolute bottom-[calc(25%-10px)] right-[-30px] text-xs text-slate-500">0</span>
            <span className="absolute bottom-[calc(25%-10px)] left-[-30px] text-xs text-slate-500">خسائر</span>

            {/* Sales Curve (simplified representation) */}
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                <path 
                    d="M0,90 C20,70 30,20 50,10 C70,20 80,40 100,60" 
                    fill="none" 
                    stroke="#3b82f6" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                />
                <text x="50" y="5" textAnchor="middle" fill="#3b82f6" fontSize="4" fontWeight="bold">منحنى المبيعات</text>
            </svg>

            {/* Vertical dashed lines for sub-stages */}
            <div className="absolute inset-0 flex justify-around items-end pb-8">
                <div className="flex flex-col items-center h-full justify-end">
                    <div className="h-full w-px bg-slate-300 border-dashed"></div>
                    <span className="text-xs text-slate-700 mt-2">النامي</span>
                </div>
                <div className="flex flex-col items-center h-full justify-end">
                    <div className="h-full w-px bg-slate-300 border-dashed"></div>
                    <span className="text-xs text-slate-700 mt-2">المستقر</span>
                </div>
                <div className="flex flex-col items-center h-full justify-end">
                    <div className="h-full w-px bg-slate-300 border-dashed"></div>
                    <span className="text-xs text-slate-700 mt-2">المتضائل</span>
                </div>
            </div>
        </div>
    </div>
  );
};
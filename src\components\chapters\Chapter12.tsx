import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter12Flowchart } from './Chapter12Flowchart';
import { Chapter12Introduction } from './Chapter12Introduction';
import { Chapter12Concept } from './Chapter12Concept';
import { Chapter12Objectives } from './Chapter12Objectives';
import { Chapter12Factors } from './Chapter12Factors';
import { Chapter12Methods } from './Chapter12Methods'; // Modified to contain only Cost/Value basis
import { Chapter12PricingStrategies } from './Chapter12PricingStrategies'; // Renamed from Chapter12Strategies
import { Chapter12Perspectives } from './Chapter12Perspectives';
import { Chapter12PricingMethodsDetails } from './Chapter12PricingMethodsDetails'; // New component for detailed pricing methods
import { BookOpen, Share2, DollarSign, Target, SlidersHorizontal, Calculator, Shapes, Scale, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 12
const chapter12Quiz = quizzes.find(q => q.chapterId === 12);

// Define sections for Chapter 12
export const chapter12Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter12Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter12Introduction /> },
  { value: "item-2", title: "مفهوم السعر وأهميته", icon: DollarSign, component: <Chapter12Concept /> },
  { value: "item-5", title: "الكلفة او القيمة كأساس لتقديم المنتجات", icon: Calculator, component: <Chapter12Methods /> },
  { value: "item-perspectives", title: "منظور البائع والمشتري", icon: Scale, component: <Chapter12Perspectives /> },
  { value: "item-3", title: "أهداف التسعير", icon: Target, component: <Chapter12Objectives /> },
  { value: "item-4", title: "العوامل المؤثرة في قرارات التسعير", icon: SlidersHorizontal, component: <Chapter12Factors /> },
  { value: "item-6", title: "طرق التسعير", icon: Shapes, component: <Chapter12PricingMethodsDetails /> }, // Changed to new component
  { value: "item-strategies", title: "استراتيجيات التسعير", icon: Shapes, component: <Chapter12PricingStrategies /> }, // New section for strategies
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter12Quiz ? <Quiz questions={chapter12Quiz.questions} chapterId={12} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter12Props {
  sections: typeof chapter12Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter12 = ({ sections, activeSectionValue, isPresentationMode }: Chapter12Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-yellow-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
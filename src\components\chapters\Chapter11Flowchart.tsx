import React from 'react';
import { ArrowDown, ArrowLeft } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-red-600 border-red-800 text-white font-bold text-lg',
    2: 'bg-red-200 border-red-400 text-red-800 font-semibold',
    3: 'bg-red-100 border-red-300 text-red-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter11Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-red-800">
        هيكلية الفصل الحادي عشر
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-md">الاتصالات التسويقية المتكاملة</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>المزيج الترويجي</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-red-50 rounded-md border-2 border-dashed border-red-200">
              <FlowBox level={3}>الإعلان</FlowBox>
              <FlowBox level={3}>ترويج المبيعات</FlowBox>
              <FlowBox level={3}>البيع الشخصي</FlowBox>
              <FlowBox level={3}>العلاقات العامة</FlowBox>
              <FlowBox level={3}>التسويق المباشر والرقمي</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>عملية الاتصال</FlowBox>
            <VerticalArrow />
            <FlowBox level={3}>خطوات تطوير الاتصال الفعال</FlowBox>
            <VerticalArrow />
            <FlowBox level={2}>وضع الميزانية الترويجية</FlowBox>
             <VerticalArrow />
            <FlowBox level={3}>طرق تحديد الميزانية</FlowBox>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>تشكيل المزيج الترويجي</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-red-50 rounded-md border-2 border-dashed border-red-200">
                <FlowBox level={3}>طبيعة كل أداة</FlowBox>
                <FlowBox level={3}>استراتيجيات الدفع والسحب</FlowBox>
            </div>
            <VerticalArrow />
            <FlowBox level={2}>المسؤولية الاجتماعية</FlowBox>
          </div>

        </div>
      </div>
    </div>
  );
};
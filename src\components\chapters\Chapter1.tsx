import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Lightbulb, Target, TrendingUp, Handshake, Store, HelpCircle, Key, Package, Blend, Star, BookCheck, DollarSign, MapPin, Megaphone, TestTube2 } from "lucide-react";
import React from "react";
import { ComparisonCard } from "./ComparisonCard";
import { WhatIsMarketed } from "./WhatIsMarketed";
import { SellingVsMarketingDiagram } from "./SellingVsMarketingDiagram";
import { SocietalMarketingTriangle } from "./SocietalMarketingTriangle";
import { Chapter1Hero } from "./Chapter1Hero";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Re-usable Section Card component for consistency
const SectionCard = ({ icon: Icon, title, children }: { icon: React.ElementType, title: string, children: React.ReactNode }) => (
  <Card className="shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
    <CardHeader className="bg-slate-50 border-b">
      <CardTitle className="flex items-center gap-3 text-2xl text-dark-gray">
        <Icon className="h-8 w-8 text-secondary-blue" />
        {title}
      </CardTitle>
    </CardHeader>
    <CardContent className="p-6 space-y-6">
      {children}
    </CardContent>
  </Card>
);

const Definition = ({ source, text }: { source: string, text: string }) => (
  <div className="p-5 border-l-8 border-blue-500 rounded-lg bg-gradient-to-r from-blue-50 to-sky-100 shadow-xl transform hover:scale-105 transition-transform duration-300">
    <h4 className="font-bold text-secondary-blue mb-2 text-lg">{source}</h4>
    <blockquote className="text-text-gray leading-relaxed italic text-base">
      "{text}"
    </blockquote>
  </div>
);

const CoreConcept = ({ icon: Icon, title, description }: { icon: React.ElementType, title: string, description: string }) => (
  <div className="flex items-start gap-4 p-4 bg-blue-50 rounded-lg h-full border border-blue-200">
    <Icon className="h-8 w-8 text-secondary-blue mt-1 flex-shrink-0" />
    <div>
      <h4 className="font-bold text-md text-dark-gray">{title}</h4>
      <p className="text-text-gray leading-relaxed">{description}</p>
    </div>
  </div>
);

const MarketingEra = ({ title, description, color }: { title: string, description: string, color: string }) => (
    <div className={`border-r-4 ${color} p-4 rounded-r-md bg-white shadow-sm`}>
        <h4 className="font-bold text-lg text-dark-gray">{title}</h4>
        <p className="text-text-gray leading-relaxed">{description}</p>
    </div>
);

const NumberedListItem = ({ number, children }: { number: string | React.ReactNode, children: React.ReactNode }) => (
    <div className="flex items-start gap-4">
        <div className="flex-shrink-0 h-8 w-8 bg-blue-100 text-secondary-blue font-bold rounded-full flex items-center justify-center">
            {number}
        </div>
        <div className="text-text-gray leading-relaxed mt-1">{children}</div>
    </div>
);

// Find the quiz for chapter 1
const chapter1Quiz = quizzes.find(q => q.chapterId === 1);

// Define sections for Chapter 1
export const chapter1Sections = [
  {
    value: "item-0",
    title: "مقدمة الفصل",
    icon: HelpCircle,
    component: <Chapter1Hero />,
  },
  {
    value: "item-1",
    title: "ما هو التسويق؟",
    icon: HelpCircle,
    component: (
      <SectionCard icon={HelpCircle} title="ما هو التسويق؟">
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-dark-gray">3 تعاريف أساسية</h3>
          <Definition
            source="تعريف جمعية التسويق الأمريكية (AMA)"
            text="القيام بأنشطة الأعمال اللازمة لأحداث تدفق مباشر للسلع والخدمات من المنتج إلى المستهلك أو المستعمل النهائي."
          />
          <Definition
            source="تعريف عام (1985)"
            text="العمليات المتعلقة بتخطيط وتنفيذ المفاهيم المتعلقة بالتسعير والترويج والتوزيع للأفكار والسلع والخدمات وذلك لتحقيق عمليات التبادل باتجاه إرضاء الأفراد ومقابلة أهداف المنظمة."
          />
          <Definition
            source="تعريف كوتلر (2018)"
            text="مجموعة من العمليات التي تقوم بها الشركات والموجهه نحو الزبون لبناء علاقة قوية ودائمة، ولخلق قيمة للزبون لعودته مرة أخرى."
          />
        </div>
        <ComparisonCard />
      </SectionCard>
    ),
  },
  {
    value: "item-2",
    title: "المفاهيم التسويقية الأساسية",
    icon: Key,
    component: (
      <SectionCard icon={Key} title="المفاهيم التسويقية الأساسية">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <CoreConcept
            icon={Target}
            title="الحاجات، الرغبات، والطلبات"
            description="الحاجة هي حالة من الشعور بالحرمان. الرغبات هي الشكل الذي تأخذه الحاجات. عندما تكون الرغبات مدعومة بالقدرة الشرائية، تتحول إلى طلبات."
          />
          <CoreConcept
            icon={Lightbulb}
            title="المنتجات، الخدمات، والتجارب"
            description="عرض السوق هو مزيج من المنتجات، الخدمات، المعلومات، أو التجارب التي تقدمها الشركة لإشباع حاجات ورغبات السوق."
          />
          <CoreConcept
            icon={TrendingUp}
            title="القيمة، الإشباع، والجودة"
            description="قيمة العميل هي تقييمه للفرق بين المنافع والتكاليف. الإشباع هو مدى تطابق أداء المنتج مع توقعات العميل."
          />
          <CoreConcept
            icon={Handshake}
            title="التبادل، الصفقات، والعلاقات"
            description="التبادل هو جوهر التسويق. التسويق الحديث يركز على بناء علاقات قوية وطويلة الأمد مع العملاء."
          />
          <CoreConcept
            icon={Store}
            title="الأسواق"
            description="السوق هو مجموعة من المشترين الفعليين والمحتملين لمنتج ما. حجم السوق يعتمد على عدد الأشخاص الذين لديهم الرغبة والقدرة على التبادل."
          />
        </div>
      </SectionCard>
    ),
  },
  {
    value: "item-3",
    title: "ماذا نسوق؟",
    icon: Package,
    component: (
      <SectionCard icon={Package} title="ماذا نسوق؟">
        <WhatIsMarketed />
      </SectionCard>
    ),
  },
  {
    value: "item-4",
    title: "تطور الفكر التسويقي",
    icon: TrendingUp,
    component: (
      <SectionCard icon={TrendingUp} title="تطور الفكر التسويقي">
        <div className="space-y-6">
            <MarketingEra
                title="مفهوم الإنتاج (Production Concept)"
                description="يفترض أن المستهلكين يفضلون المنتجات المتاحة بأسعار معقولة. التركيز على كفاءة الإنتاج والتوزيع."
                color="border-red-500"
            />
            <MarketingEra
                title="مفهوم المنتج (Product Concept)"
                description="ينص على أن المستهلكين يفضلون المنتجات ذات الجودة والأداء العالي. التركيز على تحسين المنتج."
                color="border-orange-500"
            />
            <MarketingEra
                title="مفهوم البيع (Selling Concept)"
                description="يفترض أن المستهلكين لن يشتروا ما يكفي ما لم تبذل الشركة جهدًا كبيرًا في البيع والترويج."
                color="border-yellow-500"
            />
            <div>
                <MarketingEra
                    title="المفهوم التسويقي (Marketing Concept)"
                    description="يعتمد على تحقيق أهداف الشركة من خلال معرفة حاجات ورغبات الأسواق المستهدفة وتقديم الإشباع المطلوب بشكل أفضل من المنافسين."
                    color="border-green-500"
                />
            </div>
            <div>
                <MarketingEra
                    title="المفهوم التسويقي الاجتماعي (Societal Marketing Concept)"
                    description="يدعو إلى الموازنة بين ثلاثة اعتبارات: أرباح الشركة، ورغبات المستهلكين، ومصالح المجتمع."
                    color="border-blue-500"
                />
                <SocietalMarketingTriangle />
            </div>
        </div>
        <SellingVsMarketingDiagram />
      </SectionCard>
    ),
  },
  {
    value: "item-5",
    title: "وظائف التسويق (المزيج التسويقي - 4Ps)",
    icon: Blend,
    component: (
      <SectionCard icon={Blend} title="وظائف التسويق (المزيج التسويقي - 4Ps)">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center bg-white shadow-md border-t-4 border-blue-500 hover:shadow-lg transition-shadow">
                <CardHeader>
                    <Package className="h-10 w-10 mx-auto text-secondary-blue" />
                    <CardTitle className="mt-4 text-dark-gray">المنتج (Product)</CardTitle>
                </CardHeader>
                <CardContent className="text-text-gray">
                    <p>مزيج السلع والخدمات التي تقدمها الشركة للسوق المستهدف.</p>
                </CardContent>
            </Card>
            <Card className="text-center bg-white shadow-md border-t-4 border-green-500 hover:shadow-lg transition-shadow">
                <CardHeader>
                    <DollarSign className="h-10 w-10 mx-auto text-green-500" />
                    <CardTitle className="mt-4 text-dark-gray">السعر (Price)</CardTitle>
                </CardHeader>
                <CardContent className="text-text-gray">
                    <p>المبلغ المالي الذي يجب على العملاء دفعه للحصول على المنتج.</p>
                </CardContent>
            </Card>
            <Card className="text-center bg-white shadow-md border-t-4 border-orange-500 hover:shadow-lg transition-shadow">
                <CardHeader>
                    <MapPin className="h-10 w-10 mx-auto text-orange-500" />
                    <CardTitle className="mt-4 text-dark-gray">المكان (Place)</CardTitle>
                </CardHeader>
                <CardContent className="text-text-gray">
                    <p>أنشطة الشركة التي تجعل المنتج متاحًا للعملاء المستهدفين.</p>
                </CardContent>
            </Card>
            <Card className="text-center bg-white shadow-md border-t-4 border-purple-500 hover:shadow-lg transition-shadow">
                <CardHeader>
                    <Megaphone className="h-10 w-10 mx-auto text-purple-500" />
                    <CardTitle className="mt-4 text-dark-gray">الترويج (Promotion)</CardTitle>
                </CardHeader>
                <CardContent className="text-text-gray">
                    <p>الأنشطة التي توصل مزايا المنتج إلى العملاء المستهدفين وتقنعهم بشرائه.</p>
                </CardContent>
            </Card>
        </div>
      </SectionCard>
    ),
  },
  {
    value: "item-6",
    title: "أهمية التسويق",
    icon: Star,
    component: (
      <SectionCard icon={Star} title="أهمية التسويق">
        <div>
            <h3 className="text-xl font-semibold text-dark-gray mb-4">للمنظمة</h3>
            <div className="space-y-4">
                <NumberedListItem number="1">حلقة الوصل والربط بين المنظمة والمجتمع.</NumberedListItem>
                <NumberedListItem number="2">مسؤولة عن تصريف الإنتاج وتوزيعه.</NumberedListItem>
                <NumberedListItem number="3">إجراء الدراسات والتحليل الدقيق للفرص المتاحة.</NumberedListItem>
                <NumberedListItem number="4">إعداد البرنامج التسويقي المتكامل (المزيج التسويقي).</NumberedListItem>
                <NumberedListItem number="5">التخطيط للأنشطة التسويقية المساندة.</NumberedListItem>
            </div>
        </div>
        <div className="mt-8">
            <h3 className="text-xl font-semibold text-dark-gray mb-4">على المستوى الكلي والفردي</h3>
            <div className="space-y-4">
                <NumberedListItem number="1"><strong>توفير فرص العمل:</strong> قطاع التسويق يوفر وظائف كثيرة.</NumberedListItem>
                <NumberedListItem number="2"><strong>التأثير في الناتج الكلي للاقتصاد:</strong> يساهم في تصريف الإنتاج المحلي والخارجي.</NumberedListItem>
                <NumberedListItem number="3"><strong>تنمية الاقتصاد الكلي:</strong> الأرباح تساهم في تطوير منتجات وتكنولوجيا جديدة.</NumberedListItem>
                <NumberedListItem number="4"><strong>تغيير نمط الحياة للأفراد:</strong> يقدم سلع جديدة ومطورة تغير النمط الحياتي.</NumberedListItem>
            </div>
        </div>
      </SectionCard>
    ),
  },
  {
    value: "item-7",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: (
      <SectionCard icon={TestTube2} title="اختبر معلوماتك في الفصل الأول">
        {chapter1Quiz ? <Quiz questions={chapter1Quiz.questions} chapterId={1} /> : <p>لا يوجد اختبار متاح حاليًا.</p>}
      </SectionCard>
    ),
  },
];

interface Chapter1Props {
  sections: typeof chapter1Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean;
}

export const Chapter1 = ({ sections, activeSectionValue, isPresentationMode }: Chapter1Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")}
      value={isPresentationMode ? activeSectionValue : undefined}
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-2xl font-bold text-secondary-blue hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-secondary-blue flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
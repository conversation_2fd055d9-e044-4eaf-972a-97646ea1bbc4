import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-yellow-500 border-yellow-700 text-white font-bold text-lg',
    2: 'bg-yellow-200 border-yellow-400 text-yellow-800 font-semibold',
    3: 'bg-yellow-100 border-yellow-300 text-yellow-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter12Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-yellow-800">
        هيكلية الفصل الثاني عشر
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-sm">التسعير</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-5 gap-4">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>مفهوم السعر وأهميته</FlowBox>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>أهداف التسعير</FlowBox>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>العوامل المؤثرة في قرارات التسعير</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-yellow-50 rounded-md border-2 border-dashed border-yellow-200">
              <FlowBox level={3}>العوامل الداخلية</FlowBox>
              <FlowBox level={3}>العوامل الخارجية</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>طرق التسعير</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-yellow-50 rounded-md border-2 border-dashed border-yellow-200">
              <FlowBox level={3}>على أساس الكلفة</FlowBox>
              <FlowBox level={3}>على أساس القيمة</FlowBox>
              <FlowBox level={3}>على أساس المنافسة</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>استراتيجيات التسعير</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-yellow-50 rounded-md border-2 border-dashed border-yellow-200">
              <FlowBox level={3}>تسعير المنتجات الجديدة</FlowBox>
              <FlowBox level={3}>تسعير مزيج المنتجات</FlowBox>
              <FlowBox level={3}>تعديل الأسعار</FlowBox>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, ArrowRight, XCircle, Plus, Minus, PenTool, Eraser, Circle, Square, Star, Pointer, ArrowUpRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PresentationControlsProps {
  currentSlideIndex: number;
  totalSlides: number;
  onNext: () => void;
  onPrev: () => void;
  onExit: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  isZoomInDisabled: boolean;
  isZoomOutDisabled: boolean;
  // Drawing props
  currentColor: string;
  setCurrentColor: (color: string) => void;
  currentStrokeWidth: number;
  setCurrentStrokeWidth: (width: number) => void;
  isDrawingMode: boolean;
  toggleDrawingMode: () => void;
  onClearCanvas: () => void;
  currentCursorShape: string;
  setCurrentCursorShape: (shape: string) => void;
}

export const PresentationControls = ({
  currentSlideIndex,
  totalSlides,
  onNext,
  onPrev,
  onExit,
  onZoomIn,
  onZoomOut,
  isZoomInDisabled,
  isZoomOutDisabled,
  // Drawing props
  currentColor,
  setCurrentColor,
  currentStrokeWidth,
  setCurrentStrokeWidth,
  isDrawingMode,
  toggleDrawingMode,
  onClearCanvas,
  currentCursorShape,
  setCurrentCursorShape,
}: PresentationControlsProps) => {
  const isFirstSlide = currentSlideIndex === 0;
  const isLastSlide = currentSlideIndex === totalSlides - 1;

  const colors = ['#FF0000', '#0000FF', '#000000', '#00FF00', '#FFFF00', '#FFA500'];
  const strokeWidths = [2, 5, 10, 15];
  const cursorShapes = [
    { name: 'pen', icon: PenTool },
    { name: 'arrow', icon: ArrowUpRight },
    { name: 'circle', icon: Circle },
    { name: 'square', icon: Square },
    { name: 'star', icon: Star },
    { name: 'pointer', icon: Pointer },
  ];

  return (
    <div className="fixed bottom-4 left-1/2 -translate-x-1/2 z-50 flex items-center gap-2 p-3 bg-gray-800/90 rounded-full shadow-lg flex-wrap justify-center">
      {/* Zoom Controls */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onZoomOut}
        disabled={isZoomOutDisabled}
        className="text-white hover:bg-gray-700 disabled:opacity-50"
        title="تصغير"
      >
        <Minus className="h-6 w-6" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={onZoomIn}
        disabled={isZoomInDisabled}
        className="text-white hover:bg-gray-700 disabled:opacity-50"
        title="تكبير"
      >
        <Plus className="h-6 w-6" />
      </Button>

      {/* Separator */}
      <div className="w-px h-8 bg-gray-700 mx-1" />

      {/* Drawing Mode Toggle */}
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleDrawingMode}
        className={cn(
          "text-white hover:bg-gray-700",
          isDrawingMode ? "bg-blue-600 hover:bg-blue-700" : ""
        )}
        title={isDrawingMode ? "إيقاف وضع الرسم" : "تفعيل وضع الرسم"}
      >
        <PenTool className="h-6 w-6" />
      </Button>

      {isDrawingMode && (
        <>
          {/* Separator */}
          <div className="w-px h-8 bg-gray-700 mx-1" />

          {/* Color Palette */}
          <div className="flex gap-1">
            {colors.map((color) => (
              <Button
                key={color}
                variant="ghost"
                size="icon"
                onClick={() => setCurrentColor(color)}
                className={cn(
                  "h-7 w-7 rounded-full border-2",
                  currentColor === color ? "border-white" : "border-transparent"
                )}
                style={{ backgroundColor: color }}
                title={`اللون: ${color}`}
              />
            ))}
          </div>

          {/* Separator */}
          <div className="w-px h-8 bg-gray-700 mx-1" />

          {/* Stroke Widths */}
          <div className="flex gap-1">
            {strokeWidths.map((width) => (
              <Button
                key={width}
                variant="ghost"
                size="icon"
                onClick={() => setCurrentStrokeWidth(width)}
                className={cn(
                  "h-7 w-7 rounded-full flex items-center justify-center text-white hover:bg-gray-700",
                  currentStrokeWidth === width ? "bg-gray-700" : ""
                )}
                title={`حجم الخط: ${width}`}
              >
                <span style={{ width: `${width}px`, height: `${width}px`, borderRadius: '50%', backgroundColor: 'white' }} />
              </Button>
            ))}
          </div>

          {/* Separator */}
          <div className="w-px h-8 bg-gray-700 mx-1" />

          {/* Cursor Shapes */}
          <div className="flex gap-1">
            {cursorShapes.map((shape) => (
              <Button
                key={shape.name}
                variant="ghost"
                size="icon"
                onClick={() => setCurrentCursorShape(shape.name)}
                className={cn(
                  "h-7 w-7 text-white hover:bg-gray-700",
                  currentCursorShape === shape.name ? "bg-gray-700" : ""
                )}
                title={`شكل المؤشر: ${shape.name}`}
              >
                <shape.icon className="h-5 w-5" />
              </Button>
            ))}
          </div>

          {/* Separator */}
          <div className="w-px h-8 bg-gray-700 mx-1" />

          {/* Clear Canvas */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onClearCanvas}
            className="text-red-400 hover:bg-gray-700"
            title="مسح الكل"
          >
            <Eraser className="h-6 w-6" />
          </Button>
        </>
      )}

      {/* Separator */}
      <div className="w-px h-8 bg-gray-700 mx-1" />

      {/* Navigation Controls */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onPrev}
        disabled={isFirstSlide}
        className="text-white hover:bg-gray-700 disabled:opacity-50"
        title="الشريحة السابقة"
      >
        <ArrowRight className="h-6 w-6" />
      </Button>
      <span className="text-white font-bold text-lg">
        {currentSlideIndex + 1} / {totalSlides}
      </span>
      <Button
        variant="ghost"
        size="icon"
        onClick={onNext}
        disabled={isLastSlide}
        className="text-white hover:bg-gray-700 disabled:opacity-50"
        title="الشريحة التالية"
      >
        <ArrowLeft className="h-6 w-6" />
      </Button>

      {/* Separator */}
      <div className="w-px h-8 bg-gray-700 mx-1" />

      {/* Exit Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onExit}
        className="text-red-400 hover:bg-gray-700"
        title="الخروج من وضع العرض"
      >
        <XCircle className="h-6 w-6" />
      </Button>
    </div>
  );
};
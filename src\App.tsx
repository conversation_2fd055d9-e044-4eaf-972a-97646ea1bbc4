import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import NotFound from "./pages/NotFound";
import DashboardLayout from "./pages/DashboardLayout";
import { chapters } from "./data/chapters";
import ChapterPage from "./pages/ChapterPage";
import Index from "./pages/Index";
import QuizPage from "./pages/QuizPage"; // استيراد الصفحة الجديدة

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner position="bottom-right" richColors />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<DashboardLayout />}>
            <Route index element={<Index />} />
            {chapters.map((chapter) => (
              <Route
                key={chapter.id}
                path={chapter.path}
                element={<ChapterPage chapter={chapter} />}
              />
            ))}
          </Route>
          {/* إضافة المسار الجديد المستقل للاختبارات */}
          <Route path="/quiz/:chapterId" element={<QuizPage />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
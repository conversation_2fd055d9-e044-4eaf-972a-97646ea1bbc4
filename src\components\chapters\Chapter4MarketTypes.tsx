import React from 'react';
import { ArrowDown } from 'lucide-react';

const MarketDiagram = () => {
  const marketTypes = [
    { name: "أسواق المستهلك", color: "blue" },
    { name: "أسواق الأعمال", color: "green" },
    { name: "أسواق إعادة البيع", color: "purple" },
    { name: "أسواق المؤسسات", color: "yellow" },
    { name: "الأسواق الحكومية", color: "red" },
    { name: "الأسواق الدولية", color: "indigo" },
  ];

  const colorClasses: { [key: string]: string } = {
    blue: "bg-blue-100 border-blue-300 text-blue-800",
    green: "bg-green-100 border-green-300 text-green-800",
    purple: "bg-purple-100 border-purple-300 text-purple-800",
    yellow: "bg-yellow-100 border-yellow-300 text-yellow-800",
    red: "bg-red-100 border-red-300 text-red-800",
    indigo: "bg-indigo-100 border-indigo-300 text-indigo-800",
  };

  return (
    <div className="p-6 bg-slate-50 rounded-xl border my-6">
      <h4 className="text-center font-bold text-xl mb-6 text-dark-gray">شكل (4-1): أنواع الأسواق</h4>
      <div className="flex flex-col items-center">
          <div className="bg-blue-600 text-white rounded-lg p-4 font-bold text-lg shadow-md mb-4">
              التسويق
          </div>
          <ArrowDown className="h-8 w-8 text-slate-400 mb-4" />
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 w-full max-w-3xl">
              {marketTypes.map((type, index) => (
                  <div key={index} className={`p-3 rounded-md shadow border text-center font-medium h-full flex items-center justify-center ${colorClasses[type.color]}`}>
                      {type.name}
                  </div>
              ))}
          </div>
      </div>
      <p className="text-center text-xs text-slate-500 mt-6">Source: Kotler & Armstrong, Principles of Marketing, 2018, p.75</p>
    </div>
  );
};

const MarketDefinition = ({ letter, title, children, enTitle, color }: { letter: string; title: string; children: React.ReactNode; enTitle: string; color: string }) => (
  <div className={`p-4 rounded-lg shadow-sm border-t-4 ${color}`}>
    <h4 className="font-bold text-lg text-dark-gray">{letter}. {title}</h4>
    <p className="text-sm text-slate-500 mb-2">{enTitle}</p>
    <p className="mt-1 text-text-gray leading-relaxed">{children}</p>
  </div>
);

export const Chapter4MarketTypes = () => {
  return (
    <div className="space-y-6">
      <MarketDiagram />
      <div className="space-y-4">
        <MarketDefinition letter="أ" title="أسواق المستهلك" enTitle="Consumer Markets" color="border-blue-400 bg-blue-50/50">
          وتتضمن المشترين من الأفراد والذين يكون هدفهم هو إشباع حاجاتهم الشخصية، أو الانتفاع من السلع والخدمات التي يشترونها دون أن يكون هدفهم الربح من خلال إعادة بيعها. وكما هو مثلاً في أسواق الغذاء، الملابس، الخدمات الشخصية، المنتجات المنزلية... الخ. وسنرد في فصل لاحق للبحث فيها تفصيلاً.
        </MarketDefinition>
        <MarketDefinition letter="ب" title="أسواق الأعمال" enTitle="Business Markets" color="border-green-400 bg-green-50/50">
          وهي الأسواق التي تشتري السلع والخدمات لغرض استخدامها في مجال الإنتاج لمنتجات أخرى، أو لاستخدامها لأغراض عامة في عمل المنظمة (تسهيلات). وسنرد على بحثها لاحقاً.
        </MarketDefinition>
        <MarketDefinition letter="ج" title="أسواق إعادة البيع" enTitle="Reseller Markets" color="border-purple-400 bg-purple-50/50">
          تلك الأسواق التي تقوم بشراء البضاعة أو الخدمة لإعادة بيعها مرة أخرى بهدف تحقيق الربح. ويمكن تسمية هذه السوق بسوق منظمات الأعمال إلى منظمات الأعمال (Business-to-Business B2B).
        </MarketDefinition>
        <MarketDefinition letter="د" title="أسواق المؤسسات" enTitle="Institutional Markets" color="border-yellow-400 bg-yellow-50/50">
          وهي تلك الأسواق التي تأخذ أشكال مختلفة ويمكن أن نجدها في الجامعات والمدارس، المستشفيات، دور التمريض والرعاية الصحية، والتي تقدم لمشتريها منتجات ذات عناية خاصة.
        </MarketDefinition>
        <MarketDefinition letter="هـ" title="الأسواق الحكومية" enTitle="Government Markets" color="border-red-400 bg-red-50/50">
          وهي الأسواق التي تعود للدولة وتتعامل بها مع أطراف مختلفة ويكون غرضها هو شراء سلع وخدمات لاستخدامها في تقديم خدمات عامة إلى المجتمع أو مناقلة السلع وتقديمها لمن يحتاجها.
        </MarketDefinition>
        <MarketDefinition letter="و" title="الأسواق الدولية" enTitle="International Markets" color="border-indigo-400 bg-indigo-50/50">
          وهي تلك الأسواق التي تكون خارج الحدود الإقليمية للسوق ويمكن أن تتضمن جميع ما سبق ذكره من أنواع للسوق. وهذه الأسواق لها خصوصية في التعامل ويتحكمها متغيرات كثيرة يصعب السيطرة عليها في الغالب.
        </MarketDefinition>
      </div>
    </div>
  );
};
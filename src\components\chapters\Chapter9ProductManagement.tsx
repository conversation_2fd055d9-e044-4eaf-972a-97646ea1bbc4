import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Package, Tag, Box, Ticket, LifeBuoy, Rows, Columns, Maximize, Minimize, MoveVertical, GitCommitHorizontal, Palette, Star } from 'lucide-react';
import { ProductMixTable } from './Chapter9ProductLineMixDiagram';

// Re-usable Section Card component for consistency
const SectionCard = ({ icon: Icon, title, children, color }: { icon: React.ElementType, title: string, children: React.ReactNode, color: string }) => (
    <Card className={`shadow-md border-t-4 ${color}`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-slate-800">
                <Icon className={`h-8 w-8 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
            {children}
        </CardContent>
    </Card>
);

const DecisionPoint = ({ icon: Icon, title, children }: { icon: React.ElementType, title: string, children: React.ReactNode }) => (
    <div className="p-4 bg-slate-50 rounded-lg border">
        <h5 className="font-bold text-lg text-slate-700 flex items-center gap-2 mb-2">
            <Icon className="h-5 w-5 text-purple-600" />
            {title}
        </h5>
        <p className="text-slate-600 leading-relaxed pr-7">{children}</p>
    </div>
);

export const Chapter9ProductManagement = () => {
  return (
    <div className="space-y-10">
      {/* Content from Chapter9Decisions.tsx */}
      <SectionCard icon={Package} title="1. قرارات المنتج والخدمة الفردية" color="border-t-blue-500">
          <p className="text-slate-600">يركز المسوقون على عدة قرارات هامة عند تطوير المنتجات والخدمات الفردية:</p>
          <div className="space-y-4">
              <DecisionPoint icon={Star} title="سمات المنتج (Product Attributes)">
                  تطوير المنتج يبدأ بتحديد الفوائد التي سيقدمها، والتي يتم توصيلها من خلال سمات ملموسة مثل الجودة والميزات والتصميم.
              </DecisionPoint>
              <DecisionPoint icon={LifeBuoy} title="خدمات دعم المنتج (Product Support Services)">
                  خدمة العملاء هي عنصر حاسم آخر في استراتيجية المنتج. يمكن أن تكون الخدمات الإضافية جزءًا بسيطًا أو كبيرًا من العرض الكلي.
              </DecisionPoint>
          </div>
      </SectionCard>

      <SectionCard icon={Rows} title="2. قرارات خط الإنتاج" color="border-t-green-500">
          <p className="text-slate-600">
              من الخطأ الاعتقاد بأن المنتج يمكن تسويقه لوحده أو التعامل مع صنف محدد دون أن تكون له علاقة مع منتجات أخرى تكون مجموعة مشتركة وذات صفات أو خصائص متجانسة ويمكن بيعها إلى ذات الزبون أو المجموعة من الزبائن وبنفس الطريقة التسويقية. وهذا ما يسمى بخط المنتج (Product Line).
          </p>
          <p className="text-slate-600">
              خط الإنتاج هو مجموعة من المنتجات المترابطة بشكل وثيق لأنها تعمل بطريقة مماثلة، أو تباع لنفس مجموعات العملاء، أو يتم تسويقها من خلال نفس أنواع المنافذ، أو تقع ضمن نطاقات سعرية معينة. القرار الرئيسي يتعلق بطول خط الإنتاج - أي عدد العناصر المختلفة ضمن هذا الخط. يمكن للشركات زيادة طول خط الإنتاج بطريقتين رئيسيتين:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DecisionPoint icon={GitCommitHorizontal} title="ملء الخط (Line Filling)">
                  <p>تتضمن هذه الاستراتيجية إضافة المزيد من العناصر ضمن النطاق الحالي لخط الإنتاج. الهدف من ملء الخط هو زيادة الأرباح، سد الفجوات في السوق التي قد يستغلها المنافسون، الاستفادة من الطاقة الإنتاجية الزائدة، أو تقديم مجموعة أوسع من الخيارات للعملاء. على سبيل المثال، قد تضيف شركة مشروبات نكهات جديدة إلى خط إنتاجها الحالي من المشروبات الغازية.</p>
              </DecisionPoint>
              <DecisionPoint icon={MoveVertical} title="تمديد الخط (Line Stretching)">
                  <p>تعني هذه الاستراتيجية إطالة خط الإنتاج إلى ما بعد نطاقه الحالي. يمكن أن يكون التمديد للأسفل (Downmarket Stretch) بإضافة منتجات ذات أسعار أقل لجذب شريحة أوسع من العملاء، أو للأعلى (Upmarket Stretch) بإضافة منتجات فاخرة ذات أسعار أعلى لتعزيز الصورة الذهنية للعلامة التجارية وجذب عملاء ذوي دخل أعلى. كما يمكن أن يكون التمديد في كلا الاتجاهين (Two-way Stretch) لتغطية جميع شرائح السوق. على سبيل المثال، قد تبدأ شركة سيارات فاخرة بإنتاج طرازات اقتصادية، أو العكس.</p>
              </DecisionPoint>
          </div>
      </SectionCard>

      <SectionCard icon={Columns} title="3. قرارات مزيج المنتجات (المحفظة)" color="border-t-orange-500">
          <p className="text-slate-600">
              ومزيج المنتج يمثل في حقيقته إجمالي المجاميع للمنتجات التي تتعامل بها الشركة والتي تقدمها إلى المستهلكين والتي يمكن تقسيمها على سبيل المثال في محلات السوبر ماركت.
          </p>
          <ProductMixTable />
          <p className="text-slate-600">
              مزيج المنتجات (أو محفظة المنتجات) يتكون من جميع خطوط الإنتاج والعناصر التي تقدمها شركة معينة للبيع. لمزيج المنتجات أربعة أبعاد هامة تحدد استراتيجية الشركة وتنوع عروضها في السوق:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DecisionPoint icon={Maximize} title="العرض (Width)">
                  <p>يشير عرض مزيج المنتجات إلى عدد خطوط الإنتاج المختلفة التي تحملها الشركة. على سبيل المثال، إذا كانت شركة تنتج منتجات الألبان، المشروبات الغازية، والوجبات الخفيفة، فإن لديها ثلاثة خطوط إنتاج رئيسية، وهذا يمثل عرض مزيج منتجاتها. زيادة العرض تسمح للشركة بتنويع المخاطر واستهداف شرائح سوقية مختلفة.</p>
              </DecisionPoint>
              <DecisionPoint icon={Rows} title="الطول (Length)">
                  <p>يمثل طول مزيج المنتجات إجمالي عدد العناصر أو المنتجات الفردية التي تحملها الشركة داخل جميع خطوط إنتاجها. على سبيل المثال، إذا كان خط منتجات الألبان يحتوي على الحليب، الزبادي، والجبن، فإن طول هذا الخط هو 3. الطول الكلي للمزيج هو مجموع أطوال جميع الخطوط. زيادة الطول تعني تقديم المزيد من الخيارات للعملاء ضمن كل فئة.</p>
              </DecisionPoint>
              <DecisionPoint icon={Minimize} title="العمق (Depth)">
                  <p>يشير عمق مزيج المنتجات إلى عدد الإصدارات أو المتغيرات المعروضة لكل منتج في الخط. على سبيل المثال، إذا كان منتج الحليب متوفرًا بحجم لتر، ونصف لتر، وبنكهات مختلفة (كامل الدسم، قليل الدسم، خالي الدسم)، فإن هذا يمثل عمق المنتج. زيادة العمق توفر تنوعًا أكبر للعملاء وتلبي تفضيلات دقيقة.</p>
              </DecisionPoint>
              <DecisionPoint icon={Palette} title="الاتساق (Consistency)">
                  <p>يعبر الاتساق عن مدى ارتباط خطوط الإنتاج المختلفة ببعضها البعض من حيث الاستخدام النهائي، متطلبات الإنتاج، قنوات التوزيع، أو أي عامل آخر. على سبيل المثال، تكون منتجات شركة تنتج الشامبو والبلسم والصابون ذات اتساق عالٍ لأنها جميعًا منتجات عناية شخصية وتستخدم نفس قنوات التوزيع. الاتساق العالي يمكن أن يسهل إدارة العلامة التجارية والعمليات.</p>
              </DecisionPoint>
          </div>
      </SectionCard>
    </div>
  );
};
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter7Flowchart } from './Chapter7Flowchart';
import { Chapter7Introduction } from './Chapter7Introduction';
import { Chapter7ConsumerDefinition } from './Chapter7ConsumerDefinition';
import { Chapter7ConsumerMarket } from './Chapter7ConsumerMarket';
import { Chapter7ConsumerBehaviorModel } from './Chapter7ConsumerBehaviorModel';
import { Chapter7Factors } from './Chapter7Factors';
import { Chapter7BuyingProcess } from './Chapter7BuyingProcess';
import { Chapter7AdoptionProcess } from './Chapter7AdoptionProcess';
import { BookOpen, Share2, User, ShoppingBasket, Users, SlidersHorizontal, ListOrdered, Rocket, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 7
const chapter7Quiz = quizzes.find(q => q.chapterId === 7);

// Define sections for Chapter 7
export const chapter7Sections = [
  { value: "item-1", title: "هيكلية الفصل", icon: Share2, component: <Chapter7Flowchart /> },
  { value: "item-2", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter7Introduction /> },
  { value: "item-3", title: "تعريف المستهلك", icon: User, component: <Chapter7ConsumerDefinition /> },
  { value: "item-4", title: "سوق المستهلك وأنواع السلع", icon: ShoppingBasket, component: <Chapter7ConsumerMarket /> },
  { value: "item-5", title: "نموذج سلوك المستهلك", icon: Users, component: <Chapter7ConsumerBehaviorModel /> },
  { value: "item-6", title: "العوامل المؤثرة في سلوك المستهلك", icon: SlidersHorizontal, component: <Chapter7Factors /> },
  { value: "item-7", title: "خطوات عملية اتخاذ قرار الشراء", icon: ListOrdered, component: <Chapter7BuyingProcess /> },
  { value: "item-8", title: "قرار الشراء للمنتجات الجديدة", icon: Rocket, component: <Chapter7AdoptionProcess /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter7Quiz ? <Quiz questions={chapter7Quiz.questions} chapterId={7} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter7Props {
  sections: typeof chapter7Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter7 = ({ sections, activeSectionValue, isPresentationMode }: Chapter7Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-2xl font-bold text-secondary-blue hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-secondary-blue flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
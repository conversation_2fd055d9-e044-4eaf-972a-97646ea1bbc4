import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter5Flowchart } from './Chapter5Flowchart';
import { Chapter5Introduction } from './Chapter5Introduction';
import { Chapter5Microenvironment } from './Chapter5Microenvironment';
import { Chapter5Macroenvironment } from './Chapter5Macroenvironment';
import { BookOpen, Share2, Microscope, Globe, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 5
const chapter5Quiz = quizzes.find(q => q.chapterId === 5);

// Define sections for Chapter 5
export const chapter5Sections = [
  { value: "item-1", title: "هيكلية الفصل", icon: Share2, component: <Chapter5Flowchart /> },
  { value: "item-2", title: "مقدمة ومفهوم البيئة التسويقية", icon: BookOpen, component: <Chapter5Introduction /> },
  { value: "item-3", title: "البيئة التسويقية الجزئية (الخاصة)", icon: Microscope, component: <Chapter5Microenvironment /> },
  { value: "item-4", title: "البيئة التسويقية الكلية (العامة)", icon: Globe, component: <Chapter5Macroenvironment /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter5Quiz ? <Quiz questions={chapter5Quiz.questions} chapterId={5} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter5Props {
  sections: typeof chapter5Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter5 = ({ sections, activeSectionValue, isPresentationMode }: Chapter5Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-2xl font-bold text-secondary-blue hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-secondary-blue flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
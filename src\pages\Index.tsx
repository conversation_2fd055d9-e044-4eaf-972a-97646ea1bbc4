import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { chapters } from '@/data/chapters';
import { MarketingCourseHero } from '@/components/MarketingCourseHero';
import { cn } from '@/lib/utils';

const Index = () => {
  return (
    <div className="space-y-8">
      <MarketingCourseHero />

      {/* النص تم نقله إلى هنا، داخل مكون Card جديد مع تنسيقات احترافية */}
      <Card className="w-full bg-gradient-to-br from-blue-100 to-indigo-200 border-blue-300 shadow-xl overflow-hidden mt-12">
        <CardContent className="text-center p-10">
          <h1 className="text-5xl font-extrabold text-primary-dark-blue font-changa">إدارة التسويق</h1> {/* تم تطبيق اللون والحجم هنا */}
          <p className="mt-4 text-xl text-secondary-blue max-w-3xl mx-auto">
            مرحبًا بك في منهج إدارة التسويق الشامل. استكشف المفاهيم الأساسية، الاستراتيجيات، والأدوات اللازمة لإتقان عالم التسويق الحديث.
          </p>
        </CardContent>
      </Card>
      
      <h2 className="text-3xl font-bold text-dark-gray text-center mb-6">الفصول الدراسية</h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {chapters.map((chapter) => (
          <Card key={chapter.id} className="flex flex-col h-full shadow-md hover:shadow-lg transition-shadow duration-300">
            <CardHeader className="flex-row items-center gap-4 pb-3">
              <chapter.icon className="h-8 w-8 text-secondary-blue flex-shrink-0" />
              <CardTitle className="text-xl text-dark-gray">
                الفصل {chapter.id}: {chapter.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col justify-between pt-2">
              <p className="text-text-gray text-sm mb-4">
                {/* A brief description for each chapter could go here, or keep it simple */}
                استكشف مفاهيم هذا الفصل الهامة في إدارة التسويق.
              </p>
              <Link to={chapter.path} className="block mt-auto">
                <Button className="w-full">
                  ابدأ الفصل
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Index;
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { ArrowRight } from 'lucide-react';

const CostVsValueDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (12-1): التوجه في التسعير على اساس الكلفة او القيمة</h4>
        
        {/* Cost-Based Pricing Flow */}
        <div className="mb-8">
            <h5 className="font-semibold text-center text-blue-700 mb-3">التسعير على اساس الكلفة</h5>
            <div className="flex flex-col md:flex-row items-center justify-center gap-2">
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">تصميم الجيد للمنتج</div>
                <ArrowRight className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">إقرار كلفة المنتج</div>
                <ArrowRight className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">وضع السعر على اساس الكلفة</div>
                <ArrowRight className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">إقناع المشتري بقيمة المنتج</div>
            </div>
        </div>

        {/* Value-Based Pricing Flow */}
        <div>
            <h5 className="font-semibold text-center text-green-700 mb-3">التسعير على اساس القيمة</h5>
            <div className="flex flex-col md:flex-row items-center justify-center gap-2">
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">تحقيق حاجة الزبون والقيمة المدركة</div>
                <ArrowRight className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">وضع السعر المستهدف بالتوافق مع القيمة التي يراها الزبون</div>
                <ArrowRight className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">إقرار الكلف التي يمكن عرضها</div>
                <ArrowRight className="h-6 w-6 text-slate-400 transform rotate-90 md:rotate-0" />
                <div className="p-2 border border-black bg-white text-center w-full md:w-36">تصميم المنتج لتقديم القيمة المرغوبة في السعر المحدد</div>
            </div>
        </div>
    </div>
);

export const Chapter12Methods = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-slate-800">
            الكلفة أو القيمة كأساس لتقديم المنتجات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            تعتمد الشركات على أحد المنهجين الرئيسيين عند تحديد أسعار منتجاتها: التسعير على أساس التكلفة أو التسعير على أساس القيمة. كل منهج له فلسفته الخاصة ويؤثر على كيفية تصميم المنتج وتسويقه.
          </p>
          <p>
            في التسعير على أساس التكلفة، تبدأ الشركة بتحديد تكاليف إنتاج المنتج ثم تضيف هامش ربح لتحديد السعر. أما في التسعير على أساس القيمة، تبدأ الشركة بفهم قيمة المنتج للعميل ثم تحدد السعر بناءً على هذه القيمة، ثم تعمل على تصميم المنتج وتكاليفه لتتناسب مع هذا السعر المستهدف.
          </p>
        </CardContent>
      </Card>
      <CostVsValueDiagram />
    </div>
  );
};
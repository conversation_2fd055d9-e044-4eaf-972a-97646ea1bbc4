import React from 'react';
import { ArrowDown, ArrowLeft } from 'lucide-react';

const FlowBox = ({ children, className = '' }: { children: React.ReactNode; className?: string; }) => {
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[50px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 font-semibold ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-8 w-8 text-slate-400 mx-auto my-2" />;
const HorizontalArrow = () => <ArrowLeft className="h-8 w-8 text-slate-400 mx-2 hidden md:block" />;
const MobileArrow = () => <ArrowDown className="h-8 w-8 text-slate-400 mx-auto my-2 md:hidden" />;

export const Chapter6Flowchart = () => {
  return (
    <div dir="rtl" className="p-6 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-8 text-indigo-700">
        هيكل الفصل السادس
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox className="w-full max-w-xs text-lg bg-indigo-600 border-indigo-800 text-white">إدارة المعلومات التسويقية</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="flex flex-col items-center space-y-2">
                <FlowBox className="w-full bg-sky-500 border-sky-700 text-white">مفهوم نظام المعلومات التسويقي</FlowBox>
                <VerticalArrow />
                <FlowBox className="w-full bg-sky-500 border-sky-700 text-white">المنافع من اعتماد نظام المعلومات التسويقي</FlowBox>
            </div>
            
            <div className="flex flex-col items-center space-y-2">
                <FlowBox className="w-full bg-teal-500 border-teal-700 text-white">مكونات نظام المعلومات التسويقي</FlowBox>
                <VerticalArrow />
                <div className="border-2 border-dashed border-teal-200 p-2 rounded-lg space-y-2 bg-teal-50 w-full">
                    <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">قاعدة البيانات الداخلية</FlowBox>
                    <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">الاستخبارات التسويقية</FlowBox>
                    <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">بحوث التسويق</FlowBox>
                </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
                <FlowBox className="w-full bg-cyan-500 border-cyan-700 text-white">مفهوم بحوث التسويق</FlowBox>
                <VerticalArrow />
                <FlowBox className="w-full bg-cyan-500 border-cyan-700 text-white">تعريف بحوث التسويق</FlowBox>
            </div>

            <div className="flex flex-col items-center space-y-2">
                <FlowBox className="w-full bg-purple-500 border-purple-700 text-white">خطوات اجراء البحث التسويقي</FlowBox>
            </div>
        </div>

        <VerticalArrow />
        <p className="text-slate-600 text-sm mb-2">(تتبع خطوات البحث التسويقي)</p>

        <div className="w-full flex flex-col md:flex-row-reverse justify-center items-center mt-4 gap-2">
            <FlowBox className="w-full md:w-auto bg-emerald-500 border-emerald-700 text-white">تحديد المشكلة واهداف البحث</FlowBox>
            <HorizontalArrow /> <MobileArrow />
            <FlowBox className="w-full md:w-auto bg-emerald-500 border-emerald-700 text-white">تطوير خطة البحث وجمع المعلومات</FlowBox>
            <HorizontalArrow /> <MobileArrow />
            <FlowBox className="w-full md:w-auto bg-emerald-500 border-emerald-700 text-white">تنفيذ خطة البحث وتحليل البيانات</FlowBox>
            <HorizontalArrow /> <MobileArrow />
            <FlowBox className="w-full md:w-auto bg-emerald-500 border-emerald-700 text-white">تقديم التقرير النهائي</FlowBox>
        </div>
      </div>
    </div>
  );
};
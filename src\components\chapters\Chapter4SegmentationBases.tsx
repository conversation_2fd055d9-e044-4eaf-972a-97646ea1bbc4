import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Globe, Users, BrainCircuit, BarChart3, type LucideIcon } from 'lucide-react';

interface SegmentationBaseCardProps {
  icon: LucideIcon;
  title: string;
  items: string[];
  color: string;
}

const SegmentationBaseCard = ({ icon: Icon, title, items, color }: SegmentationBaseCardProps) => (
  <Card className={`h-full flex flex-col border-t-4 ${color}`}>
    <CardHeader className="flex-row items-center gap-3 pb-4">
      <Icon className="h-8 w-8 text-dark-gray flex-shrink-0" />
      <CardTitle className="text-xl text-dark-gray">{title}</CardTitle>
    </CardHeader>
    <CardContent className="flex-grow">
      <ul className="space-y-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-center gap-2 text-text-gray">
            <span className="h-2 w-2 bg-slate-400 rounded-full"></span>
            <span>{item}</span>
          </li>
        ))}
      </ul>
    </CardContent>
  </Card>
);

const segmentationBases = [
    {
        icon: Globe,
        title: "1. التجزئة الجغرافية",
        items: ["الدول", "المناطق", "الولايات", "المدن"],
        color: "border-blue-500"
    },
    {
        icon: Users,
        title: "2. التجزئة الديموغرافية",
        items: ["العمر", "الجنس", "حجم العائلة", "الدخل", "المهنة", "التعليم"],
        color: "border-green-500"
    },
    {
        icon: BrainCircuit,
        title: "3. التجزئة النفسية",
        items: ["الطبقة الاجتماعية", "نمط الحياة", "الشخصية"],
        color: "border-purple-500"
    },
    {
        icon: BarChart3,
        title: "4. التجزئة السلوكية",
        items: ["المناسبات", "المنافع", "حالة المستخدم", "معدل الاستخدام"],
        color: "border-orange-500"
    }
];

export const Chapter4SegmentationBases = () => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-4">
        <h3 className="text-2xl font-bold text-dark-gray">ثالثاً: أسس تجزئة سوق المستهلك</h3>
        <p className="text-slate-500 text-sm">شكل (4-2): المتغيرات الرئيسة لتجزئة سوق المستهلك</p>
      </div>
      <p className="text-text-gray leading-relaxed">
        لا يوجد هناك طريقة واحدة لتجزئة السوق، وعلى المسوق أن يجرب أسس مختلفة للتجزئة، وبشكل منفرد أو مزدوج، لإيجاد أفضل طريقة للنظر إلى هيكل السوق. والشكل (4-2) يوضح المتغيرات الرئيسة التي يمكن اعتمادها في تجزئة سوق المستهلك وهي:
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 pt-4">
        {segmentationBases.map((base) => (
          <SegmentationBaseCard key={base.title} {...base} />
        ))}
      </div>
    </div>
  );
};
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ShoppingCart, Building, Zap, ShoppingBasket, Gem, HelpCircle, Wrench, Package, Briefcase } from 'lucide-react';

const ProductTypeCard = ({ icon: Icon, title, enTitle, definition, examples, color }: { icon: React.ElementType, title: string, enTitle: string, definition: string, examples: string, color: string }) => (
    <Card className={`h-full flex flex-col border-t-4 ${color} shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-xl text-dark-gray">
                <Icon className={`h-7 w-7 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
            <p className="text-sm text-slate-500 -mt-2">{enTitle}</p>
        </CardHeader>
        <CardContent className="flex-grow flex flex-col">
            <p className="text-text-gray mb-4 italic">"{definition}"</p>
            <div className="mt-auto">
                <p className="text-sm text-slate-500 bg-slate-50 p-2 rounded-md"><span className="font-semibold">أمثلة:</span> {examples}</p>
            </div>
        </CardContent>
    </Card>
);

export const Chapter9Classification = () => {
  return (
    <div className="space-y-8">
        <div>
            <h3 className="text-2xl font-bold text-dark-gray flex items-center gap-3 mb-4">
                <ShoppingCart className="h-8 w-8 text-blue-600" />
                1. المنتجات الاستهلاكية (Consumer Products)
            </h3>
            <p className="text-text-gray leading-relaxed mb-6">
                هي المنتجات والخدمات التي يشتريها المستهلكون النهائيون للاستهلاك الشخصي. قام المسوقون بتصنيفها إلى أربع فئات رئيسية بناءً على عادات الشراء لدى المستهلكين.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <ProductTypeCard
                    icon={Zap}
                    title="السلع الميسرة"
                    enTitle="Convenience Products"
                    color="border-t-green-500"
                    definition="منتجات وخدمات استهلاكية يشتريها العملاء بشكل متكرر، وفوري، وبأقل قدر من المقارنة والجهد."
                    examples="المنظفات، الحلوى، الصحف، الوجبات السريعة."
                />
                <ProductTypeCard
                    icon={ShoppingBasket}
                    title="سلع التسوق"
                    enTitle="Shopping Products"
                    color="border-t-orange-500"
                    definition="منتجات وخدمات استهلاكية أقل تكرارًا في الشراء، حيث يقارن العملاء بعناية بينها من حيث الملاءمة، الجودة، السعر، والتصميم."
                    examples="الأثاث، الملابس، السيارات المستعملة، الأجهزة المنزلية الرئيسية."
                />
                <ProductTypeCard
                    icon={Gem}
                    title="السلع الخاصة"
                    enTitle="Specialty Products"
                    color="border-t-purple-500"
                    definition="منتجات وخدمات استهلاكية ذات خصائص فريدة أو هوية علامة تجارية قوية، يكون المشترون على استعداد لبذل جهد خاص لشرائها."
                    examples="السيارات الفاخرة، الساعات باهظة الثمن، الملابس المصممة خصيصًا."
                />
                <ProductTypeCard
                    icon={HelpCircle}
                    title="السلع غير المطلوبة"
                    enTitle="Unsought Products"
                    color="border-t-red-500"
                    definition="منتجات استهلاكية لا يعرف المستهلك بوجودها، أو يعرف بها ولكنه لا يفكر عادة في شرائها."
                    examples="التأمين على الحياة، خدمات التبرع بالدم، خدمات التخطيط للجنازات."
                />
            </div>
        </div>

        <div className="pt-8 border-t">
            <h3 className="text-2xl font-bold text-dark-gray flex items-center gap-3 mb-4">
                <Building className="h-8 w-8 text-blue-600" />
                2. المنتجات الصناعية (Industrial Products)
            </h3>
            <p className="text-text-gray leading-relaxed mb-6">
                هي المنتجات التي يتم شراؤها من قبل الأفراد والمنظمات لمزيد من المعالجة أو للاستخدام في إدارة الأعمال. الفرق الرئيسي بين المنتج الاستهلاكي والصناعي يكمن في الغرض من شرائه.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <ProductTypeCard
                    icon={Package}
                    title="المواد والأجزاء"
                    enTitle="Materials and Parts"
                    color="border-t-gray-500"
                    definition="تشمل المواد الخام (مثل القمح، القطن، النفط الخام) والمواد والأجزاء المصنعة (مثل المحركات، الإطارات)."
                    examples="الحديد، الأسمنت، الأسلاك، المحركات الصغيرة."
                />
                <ProductTypeCard
                    icon={Briefcase}
                    title="السلع الرأسمالية"
                    enTitle="Capital Items"
                    color="border-t-gray-500"
                    definition="منتجات صناعية تساعد في إنتاج أو عمليات المشتري، وتشمل التركيبات والملحقات."
                    examples="المباني (المصانع، المكاتب)، المعدات الثابتة (المولدات، المصاعد)، والمعدات الملحقة (الأدوات اليدوية، شاحنات الرفع)."
                />
                <ProductTypeCard
                    icon={Wrench}
                    title="اللوازم والخدمات"
                    enTitle="Supplies and Services"
                    color="border-t-gray-500"
                    definition="تشمل لوازم التشغيل (مثل مواد التشحيم، الفحم، الورق) وخدمات الصيانة والإصلاح والاستشارات التجارية."
                    examples="طلاء النوافذ، إصلاح أجهزة الكمبيوتر، الخدمات الإعلانية."
                />
            </div>
        </div>
    </div>
  );
};
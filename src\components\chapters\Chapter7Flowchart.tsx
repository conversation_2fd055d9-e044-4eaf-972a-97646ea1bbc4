import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '' }: { children: React.ReactNode; className?: string; }) => {
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[50px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 font-semibold ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-500 mx-auto my-2" />;

export const Chapter7Flowchart = () => {
  return (
    <div dir="rtl" className="p-6 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-8 text-blue-800">
        هيكلية الفصل السابع
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox className="max-w-md bg-blue-600 border-blue-800 text-white text-lg font-bold">أسواق المستهلك وسلوك الشراء</FlowBox>
        <VerticalArrow />
        
        <div className="w-full overflow-x-auto pb-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 min-w-[1100px] pt-4 border-t-2 border-slate-300">
            
            {/* Column 1 */}
            <div className="flex flex-col items-center space-y-2">
              <FlowBox className="bg-red-500 border-red-700 text-white">قرار الشراء للمنتجات الجديدة</FlowBox>
            </div>

            {/* Column 2 */}
            <div className="flex flex-col items-center space-y-2">
              <FlowBox className="bg-orange-500 border-orange-700 text-white">دوافع شراء المستهلك</FlowBox>
              <VerticalArrow />
              <div className="space-y-1 w-full p-2 bg-orange-50 rounded-md border-2 border-dashed border-orange-200">
                <FlowBox className="bg-orange-100 border-orange-300 text-orange-800 text-sm">الأولية والانتقائية</FlowBox>
                <FlowBox className="bg-orange-100 border-orange-300 text-orange-800 text-sm">العاطفية والعقلية</FlowBox>
                <FlowBox className="bg-orange-100 border-orange-300 text-orange-800 text-sm">النفسية والاجتماعية</FlowBox>
                <FlowBox className="bg-orange-100 border-orange-300 text-orange-800 text-sm">المعاملة</FlowBox>
              </div>
            </div>

            {/* Column 3 */}
            <div className="flex flex-col items-center space-y-2">
              <FlowBox className="bg-purple-500 border-purple-700 text-white">الخطوات في عملية اتخاذ قرار الشراء</FlowBox>
              <VerticalArrow />
              <FlowBox className="bg-purple-100 border-purple-300 text-purple-800 text-sm">ادراك المشكلة</FlowBox>
              <VerticalArrow />
              <FlowBox className="bg-purple-100 border-purple-300 text-purple-800 text-sm">البحث عن المعلومات</FlowBox>
              <VerticalArrow />
              <FlowBox className="bg-purple-100 border-purple-300 text-purple-800 text-sm">تقييم البدائل</FlowBox>
              <VerticalArrow />
              <FlowBox className="bg-purple-100 border-purple-300 text-purple-800 text-sm">اتخاذ قرار الشراء</FlowBox>
              <VerticalArrow />
              <FlowBox className="bg-purple-100 border-purple-300 text-purple-800 text-sm">شعور ما بعد الشراء</FlowBox>
            </div>

            {/* Column 4 */}
            <div className="flex flex-col items-center space-y-2">
              <FlowBox className="bg-green-500 border-green-700 text-white">الخصائص المؤثرة في سلوك المستهلك</FlowBox>
              <VerticalArrow />
              <div className="space-y-1 w-full p-2 bg-green-50 rounded-md border-2 border-dashed border-green-200">
                <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">الثقافية</FlowBox>
                <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">الاجتماعية</FlowBox>
                <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">الشخصية</FlowBox>
                <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">النفسية</FlowBox>
              </div>
            </div>

            {/* Column 5 */}
            <div className="flex flex-col items-center space-y-2">
              <FlowBox className="bg-cyan-500 border-cyan-700 text-white">المستهلك</FlowBox>
              <VerticalArrow />
              <FlowBox className="bg-cyan-100 border-cyan-300 text-cyan-800 text-sm">سوق المستهلك</FlowBox>
              <VerticalArrow />
              <FlowBox className="bg-cyan-100 border-cyan-300 text-cyan-800 text-sm">أنموذج سلوك المستهلك</FlowBox>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};
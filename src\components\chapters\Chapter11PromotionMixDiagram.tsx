import React from 'react';

const Box = ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={`p-2 border bg-white shadow rounded text-center font-semibold text-slate-700 flex items-center justify-center text-sm h-16 w-32 ${className}`}>
        {children}
    </div>
);

export const Chapter11PromotionMixDiagram = () => {
    return (
        <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
            <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (11-3): التكامل بين عناصر المزيج الترويجي</h4>
            <div className="relative w-full max-w-lg mx-auto h-96">
                {/* Central Box */}
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                    <Box className="bg-blue-100 border-blue-300 text-blue-800 h-24 w-40">الاتساق والوضوح والتكامل مع رسائل الشركة لعلامتها</Box>
                </div>

                {/* Corner Boxes */}
                <div className="absolute top-0 left-0"><Box>الإعلان</Box></div>
                <div className="absolute top-0 right-0"><Box>البيع الشخصي</Box></div>
                <div className="absolute bottom-16 left-0"><Box>ترويج المبيعات</Box></div>
                <div className="absolute bottom-16 right-0"><Box>العلاقات العامة</Box></div>
                
                {/* Bottom Box */}
                <div className="absolute bottom-0 left-1/2 -translate-x-1/2">
                    <Box className="w-56 bg-green-100 border-green-300 text-green-800">التسويق المباشر والتسويق الرقمي</Box>
                </div>

                {/* Arrows using SVG */}
                <svg className="absolute inset-0 w-full h-full overflow-visible" style={{ direction: 'ltr' }}>
                    <defs>
                        <marker id="arrowhead-end" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="4" markerHeight="4" orient="auto-start-reverse">
                            <path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b" />
                        </marker>
                        <marker id="arrowhead-start" viewBox="0 0 10 10" refX="2" refY="5" markerWidth="4" markerHeight="4" orient="auto-start-reverse">
                            <path d="M 10 0 L 0 5 L 10 10 z" fill="#64748b" />
                        </marker>
                    </defs>

                    {/* Horizontal Arrow */}
                    <line x1="135" y1="32" x2="350" y2="32" stroke="#64748b" strokeWidth="1.5" markerEnd="url(#arrowhead-end)" markerStart="url(#arrowhead-start)" />
                    
                    {/* Vertical Arrow Left */}
                    <line x1="64" y1="70" x2="64" y2="230" stroke="#64748b" strokeWidth="1.5" markerEnd="url(#arrowhead-end)" markerStart="url(#arrowhead-start)" />

                    {/* Vertical Arrow Right */}
                    <line x1="421" y1="70" x2="421" y2="230" stroke="#64748b" strokeWidth="1.5" markerEnd="url(#arrowhead-end)" markerStart="url(#arrowhead-start)" />

                    {/* Diagonal Arrow Bottom Left */}
                    <line x1="100" y1="260" x2="180" y2="320" stroke="#64748b" strokeWidth="1.5" markerEnd="url(#arrowhead-end)" />

                    {/* Diagonal Arrow Bottom Right */}
                    <line x1="385" y1="260" x2="305" y2="320" stroke="#64748b" strokeWidth="1.5" markerEnd="url(#arrowhead-end)" />
                </svg>
            </div>
        </div>
    );
};
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { AreaChart } from 'lucide-react';

export const Chapter2Hero = () => {
  return (
    <Card className="w-full bg-gradient-to-br from-indigo-50 to-purple-100 border-indigo-200 shadow-lg overflow-hidden mb-8">
      <div className="p-8 flex flex-col md:flex-row items-center justify-center gap-8">
        <div className="flex-shrink-0">
          <AreaChart className="h-24 w-24 text-indigo-500 opacity-80" />
        </div>
        <div className="text-center md:text-right">
          <h1 className="text-4xl font-bold text-slate-800">الفصل الثاني: إدارة التسويق</h1>
          <p className="mt-2 text-lg text-slate-600 max-w-2xl">
            استكشف المبادئ والعمليات الأساسية لإدارة التسويق الفعالة، من التحليل والتخطيط إلى التنفيذ والرقابة.
          </p>
        </div>
      </div>
    </Card>
  );
};
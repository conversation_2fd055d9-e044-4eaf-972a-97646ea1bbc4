import React from 'react';
import { ArrowDown, ArrowLeft } from 'lucide-react';

const FlowBox = ({ children, className = '' }: { children: React.ReactNode; className?: string; }) => {
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[50px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 font-semibold ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-500 mx-auto my-2" />;

export const Chapter1Flowchart = () => {
  return (
    <div className="p-6 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-8 text-blue-800">
        شكل (1-1): محتويات الفصل الأول
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox className="w-full max-w-md bg-blue-600 border-blue-800 text-white text-lg">مدخل في التسويق</FlowBox>
        <VerticalArrow />

        <div className="w-full grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
          {/* Right Column */}
          <div className="flex flex-col items-center space-y-4">
            <FlowBox className="bg-sky-500 border-sky-700 text-white">ماذا يعني التسويق؟</FlowBox>
            <VerticalArrow />
            <FlowBox className="bg-sky-400 border-sky-600 text-white">تعريف التسويق</FlowBox>
            <VerticalArrow />
            <FlowBox className="bg-sky-400 border-sky-600 text-white">ماذا نسوق؟</FlowBox>
            <VerticalArrow />
            <FlowBox className="bg-sky-400 border-sky-600 text-white">مفهوم التسويق</FlowBox>
          </div>

          {/* Middle Column */}
          <div className="flex flex-col items-center space-y-4">
            <FlowBox className="bg-teal-500 border-teal-700 text-white">التوجهات الفلسفية لتطور مفهوم التسويق</FlowBox>
            <div className="w-full p-2 bg-teal-50 rounded-md border-2 border-dashed border-teal-200 space-y-2">
              <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">الإنتاجي</FlowBox>
              <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">المنتج</FlowBox>
              <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">البيعي</FlowBox>
              <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">التسويقي</FlowBox>
              <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">المجتمعي</FlowBox>
            </div>
          </div>

          {/* Left Column */}
          <div className="flex flex-col items-center space-y-4">
            <FlowBox className="bg-cyan-500 border-cyan-700 text-white">المسؤولية الاجتماعية لإدارات التسويق</FlowBox>
          </div>
        </div>

        {/* Connector to Importance */}
        <div className="flex justify-center items-center my-6 w-full">
            <div className="w-full h-px bg-slate-400 lg:w-1/3"></div>
            <div className="flex-shrink-0 px-2">
                <ArrowDown className="h-8 w-8 text-slate-600" />
            </div>
            <div className="w-full h-px bg-slate-400 lg:w-1/3"></div>
        </div>


        <FlowBox className="w-full max-w-sm bg-blue-600 border-blue-800 text-white text-lg">أهمية التسويق</FlowBox>
        <VerticalArrow />

        <div className="flex justify-center gap-8 w-full max-w-sm">
          <FlowBox className="flex-1 bg-indigo-500 border-indigo-700 text-white">للمنظمة</FlowBox>
          <FlowBox className="flex-1 bg-indigo-500 border-indigo-700 text-white">للاقتصاد الكلي</FlowBox>
        </div>
      </div>
    </div>
  );
};
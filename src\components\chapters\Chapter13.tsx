import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter13Flowchart } from './Chapter13Flowchart';
import { Chapter13Introduction } from './Chapter13Introduction';
import { Chapter13Concept } from './Chapter13Concept';
import { Chapter13Importance } from './Chapter13Importance';
import { Chapter13AddValue } from './Chapter13AddValue';
import { Chapter13MarketingChannels } from './Chapter13MarketingChannels';
import { Chapter13ServiceChannels } from './Chapter13ServiceChannels';
import { Chapter13Structure } from './Chapter13Structure';
import { Chapter13Behavior } from './Chapter13Behavior';
import { Chapter13FactorsAffectingSelection } from './Chapter13FactorsAffectingSelection'; // Import the new component
import { BookOpen, Share2, Waypoints, Settings, DollarSign, LayoutGrid, <PERSON>, Handshake, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>e, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 13
const chapter13Quiz = quizzes.find(q => q.chapterId === 13);

// Define sections for Chapter 13
export const chapter13Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter13Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter13Introduction /> },
  { value: "item-2", title: "مفهوم وتعريف القناة التسويقية", icon: Waypoints, component: <Chapter13Concept /> },
  { value: "item-3", title: "أهمية القناة التسويقية", icon: DollarSign, component: <Chapter13Importance /> },
  { value: "item-add-value", title: "القيمة المضافة للقناة التسويقية", icon: DollarSign, component: <Chapter13AddValue /> },
  { value: "item-channels", title: "أنواع المنافذ التسويقية (السلع)", icon: LayoutGrid, component: <Chapter13MarketingChannels /> },
  { value: "item-service-channels", title: "منافذ تسويق الخدمات", icon: Wrench, component: <Chapter13ServiceChannels /> },
  { value: "item-structure", title: "هيكلة القناة التسويقية", icon: Settings, component: <Chapter13Structure /> },
  { value: "item-behavior", title: "السلوك في القناة التسويقية", icon: GitCompare, component: <Chapter13Behavior /> },
  { value: "item-factors-selection", title: "العوامل المؤثرة في اختيار القناة التسويقية", icon: Handshake, component: <Chapter13FactorsAffectingSelection /> }, // New section for Factors Affecting Selection
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter13Quiz ? <Quiz questions={chapter13Quiz.questions} chapterId={13} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter13Props {
  sections: typeof chapter13Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter13 = ({ sections, activeSectionValue, isPresentationMode }: Chapter13Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-indigo-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
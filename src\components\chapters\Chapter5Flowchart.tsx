import React from 'react';
import { ArrowDown, Globe, Microscope } from 'lucide-react';

const FlowBox = ({ children, className = '', icon: Icon }: { children: React.ReactNode; className?: string; icon?: React.ElementType }) => {
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-3 text-center shadow-lg flex items-center justify-center gap-2 min-h-[60px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 font-semibold ${className}`}>
      {Icon && <Icon className="h-5 w-5" />}
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => (
  <div className="flex justify-center items-center my-2 relative h-8">
    <div className="w-px bg-slate-400 h-full absolute"></div>
    <ArrowDown className="h-5 w-5 text-slate-600 relative bg-slate-100 px-1" />
  </div>
);

export const Chapter5Flowchart = () => {
  return (
    <div dir="rtl" className="p-6 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-8 text-blue-800">
        هيكلية الفصل الخامس
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox className="w-full max-w-sm text-xl bg-blue-600 border-blue-800 text-white">البيئة التسويقية</FlowBox>
        <VerticalArrow />
        <div className="w-full max-w-5xl grid grid-cols-1 md:grid-cols-3 gap-6 relative">
          <div className="absolute top-[-1rem] left-0 right-0 mx-auto w-2/3 h-px bg-slate-400 hidden md:block"></div>
          
          <div className="flex flex-col items-center relative">
            <div className="absolute top-[-1rem] w-px h-4 bg-slate-400 hidden md:block"></div>
            <FlowBox className="w-full bg-teal-500 border-teal-700 text-white">مفهوم البيئة التسويقية والمبررات</FlowBox>
          </div>
          
          <div className="flex flex-col items-center relative">
            <div className="absolute top-[-1rem] w-px h-4 bg-slate-400 hidden md:block"></div>
            <FlowBox className="w-full bg-green-500 border-green-700 text-white" icon={Microscope}>البيئة التسويقية الجزئية (الخاصة)</FlowBox>
          </div>

          <div className="flex flex-col items-center relative">
            <div className="absolute top-[-1rem] w-px h-4 bg-slate-400 hidden md:block"></div>
            <FlowBox className="w-full bg-purple-500 border-purple-700 text-white" icon={Globe}>البيئة التسويقية الكلية (العامة)</FlowBox>
          </div>
        </div>
      </div>
    </div>
  );
};
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON>culator, Gem, Swords, ArrowRight } from 'lucide-react';

// Diagram Component for Image 1.PNG
const PricingMethodsFlowchart = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (12-5): الطرق المعتمدة في التسعير</h4>
        <div className="flex flex-col items-center">
            <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md mb-6">
                طرق التسعير
            </div>
            <div className="flex justify-center w-full gap-4">
                {/* Branch 1: Cost-Based */}
                <div className="flex flex-col items-center w-1/3">
                    <div className="bg-white p-3 border border-black rounded-md shadow-sm text-center font-semibold text-black w-full">
                        على أساس التكاليف
                    </div>
                    <ArrowRight className="h-6 w-6 text-slate-500 my-2 transform rotate-90" />
                    <div className="flex flex-col items-center space-y-2 w-full">
                        <div className="bg-white p-2 border border-black rounded-md shadow-sm text-center text-sm text-black w-full">الكلفة + هامش ربح</div>
                        <div className="bg-white p-2 border border-black rounded-md shadow-sm text-center text-sm text-black w-full">نقطة التعادل</div>
                    </div>
                </div>

                {/* Branch 2: Value-Based */}
                <div className="flex flex-col items-center w-1/3">
                    <div className="bg-white p-3 border border-black rounded-md shadow-sm text-center font-semibold text-black w-full">
                        على أساس القيمة
                    </div>
                    <ArrowRight className="h-6 w-6 text-slate-500 my-2 transform rotate-90" />
                    <div className="flex flex-col items-center space-y-2 w-full">
                        <div className="bg-white p-2 border border-black rounded-md shadow-sm text-center text-sm text-black w-full">القيمة المدركة</div>
                        <div className="bg-white p-2 border border-black rounded-md shadow-sm text-center text-sm text-black w-full">حساسية السعر</div>
                    </div>
                </div>

                {/* Branch 3: Competition-Based */}
                <div className="flex flex-col items-center w-1/3">
                    <div className="bg-white p-3 border border-black rounded-md shadow-sm text-center font-semibold text-black w-full">
                        على أساس المنافسة
                    </div>
                    <ArrowRight className="h-6 w-6 text-slate-500 my-2 transform rotate-90" />
                    <div className="flex flex-col items-center space-y-2 w-full">
                        <div className="bg-white p-2 border border-black rounded-md shadow-sm text-center text-sm text-black w-full">الأسعار الجارية</div>
                        <div className="bg-white p-2 border border-black rounded-md shadow-sm text-center text-sm text-black w-full">قادة السعر</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
);

const BreakEvenDiagram = () => (
    <div dir="rtl" className="my-6 p-4 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-lg mb-4 text-slate-800">شكل (12-6): تحليل نقطة التعادل</h4>
        <div className="relative w-full h-64">
            {/* Y-axis */}
            <div className="absolute top-0 right-0 h-full w-px bg-black"></div>
            <span className="absolute top-1/2 right-[-25px] -translate-y-1/2 text-sm text-slate-600 transform -rotate-90">المبلغ (بالدينار)</span>
            {/* X-axis */}
            <div className="absolute bottom-0 left-0 w-full h-px bg-black"></div>
            <span className="absolute bottom-[-25px] left-1/2 -translate-x-1/2 text-sm text-slate-600">الوحدات المباعة</span>
            
            {/* Lines */}
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                {/* منحنى قيمة المبيعات */}
                <line x1="0" y1="100" x2="100" y2="0" stroke="#3b82f6" strokeWidth="2" />
                <text x="80" y="5" fill="#3b82f6" fontSize="4">منحنى قيمة المبيعات</text>
                
                {/* التكاليف الكلية (Total Cost) - This line is not explicitly labeled in the image but is implied by the intersection */}
                {/* I will draw it based on the intersection point and the fixed cost line */}
                <line x1="0" y1="60" x2="100" y2="20" stroke="#10b981" strokeWidth="2" />
                <text x="70" y="35" fill="#10b981" fontSize="4">التكاليف الكلية</text>

                {/* التكاليف الثابتة (Fixed Cost) */}
                <line x1="0" y1="60" x2="100" y2="60" stroke="#ef4444" strokeWidth="2" strokeDasharray="4,4" />
                <text x="70" y="55" fill="#ef4444" fontSize="4">التكاليف الثابتة</text>
                
                {/* نقطة التعادل (Break-even point) */}
                <circle cx="43" cy="43" r="1.5" fill="#f59e0b" />
                <line x1="43" y1="43" x2="43" y2="100" stroke="#f59e0b" strokeWidth="1" strokeDasharray="2,2" />
                <text x="35" y="38" fill="#f59e0b" fontSize="4">نقطة التعادل</text>
                
                {/* الأرباح (Profits) - Shaded area, represented by text */}
                <text x="60" y="20" fill="#3b82f6" fontSize="4" fontWeight="bold">الأرباح</text>
                {/* الخسارة (Loss) - Shaded area, represented by text */}
                <text x="20" y="80" fill="#ef4444" fontSize="4" fontWeight="bold">الخسارة</text>
            </svg>
        </div>
    </div>
);

const MethodCard = ({ icon: Icon, title, children, color }: { icon: React.ElementType; title: string; children: React.ReactNode; color: string }) => (
    <Card className={`shadow-md border-t-4 ${color}`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-slate-800">
                <Icon className={`h-8 w-8 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4 text-slate-700 leading-relaxed">
            {children}
        </CardContent>
    </Card>
);

export const Chapter12PricingMethodsDetails = () => {
  return (
    <div className="space-y-10">
        <p className="text-slate-700 leading-relaxed">
            بعد تحديد الأهداف المتعلقة بالسعر والتي على ضوئها يتحدد مسار عمل إدارة الشركة في تعاملها مع الأسعار، وعلى وفق العوامل المؤثرة في قراراتها.
        </p>
        <PricingMethodsFlowchart />

      <MethodCard icon={Calculator} title="1. الأسعار المعتمدة على التكاليف Prices Based on costs" color="border-t-blue-500">
        <p>
            تعتمد الطرق المنضوية تحت هذه المجموعة عنصر التكلفة كأساس في إقرار السعر. حيث يحدد سعر بيع الوحدة الواحدة مساوياً لإجمالي التكاليف المترتبة على إنتاجها وتسويقها، ومضافاً إليها مقدار الربح الذي يتحدد تبعاً لظروف السوق وحالة المنافسة. ومن أبرز هذه الطرق هي:
        </p>
        <h4 className="font-bold text-lg">أ. التسعير على أساس الكلفة الكاملة (الكلفة زائد) Cost – Plus Pricing</h4>
        <p>
          يتم تحديد السعر وفق هذه الطريقة على أساس احتساب جميع التكاليف وتحديد الأرباح كزيادة معينة على مجموع هذه التكاليف. والدافع الرئيسي وراء استخدام هذه الطريقة، هو رغبة المشروع في ضمان استرجاع كافة الكلف المنفقة سواء كانت متغيرة أم ثابتة، زائداً نسبة معينة من الربح، وبهدف استمراريتها في البقاء على الأمد الطويل. ولعل من أبرز الفوائد المتوخاة من استخدامها هي:
        </p>
        <ul className="list-disc pr-6 space-y-2">
            <li>أنها وسيلة سهلة وسريعة لإيجاد أسعار مقبولة للسلع التي تتعامل بها المنظمة ومهما كان عددها.</li>
            <li>أنها وسيلة للحصول على الأرباح العادلة وليس لأكبر الأرباح.</li>
            <li>تمكن من وضع سعر مستقر نسبياً لا يتأثر بالتقلبات في الطلب.</li>
            <li>أنها توفر وسيلة للإدارة العليا لتخويل صلاحية وضع الأسعار إلى المستويات الإدارية التابعة لها، لأن القضية لا تعدو عن كونها تطبيق معادلة محددة مسبقاً.</li>
        </ul>
        <p>
            والمقابل فإنه يوجه لهذه الطريقة بعض الانتقادات ولعل أبرزها هي إهمالها للظروف الخارجية المحيطة بالمشروع وكونها لا تعكس ردود الفعل لرغبات المستهلكين وقدرتهم الشرائية.
        </p>
        <h4 className="font-bold text-lg">ب. تحليل نقطة التعادل Break – Even Analysis</h4>
        <p>
            لتحليل العلاقة ما بين التكاليف والربحية التجارية تعتمد نقطة التعادل كقيادة لذلك، فهي وسيلة بيانية تجمع ما بين النفقات والإيرادات من ناحية، وبين حجم النشاط أو العمليات من ناحية أخرى ويفسر هذه العلاقة من خلال الشكل (12-6) حيث تتكون مجموعة التكاليف من التكاليف الثابتة، والتي لا تتأثر عادة بالتغيرات الحاصلة في حجم النشاط، والتكاليف الأخرى هي المتغيرة والتي تكون على العكس متأثرة في أي تغيير حاصل في حجم إنتاج المنظمة.
        </p>
        <BreakEvenDiagram />
        <p>
            وتمثل نقطة التعادل المرحلة التي تساوي فيها الإيرادات مع النفقات بحيث لا يوجد هناك أي ربح أو خسارة. لكنها تتحمل خسارة قبل الوصول إلى نقطة التعادل، وكلما انخفض مستوى حجم المبيعات كلما كبرت الخسائر، أما لو تعدت هذه النقطة، فإن الربح يبدأ في الظهور ويرتفع بارتفاع مستوى المبيعات.
        </p>
        <p>
            ونخلص إلى القول بأنه من المفيد جداً أن تكون نقطة التعادل في مستوى منخفض بدلاً من أن تكون مرتفعة، حيث كلما ارتفعت نقطة التعادل كلما قلت فرصة المنظمة في تحقيق الأرباح خلال فترة زمنية قادمة. وهذا يتوقف أساساً على فهم طبيعة السوق التي تعمل بها المنظمة.
        </p>
        <p>
            وبنفس الوقت فإنه يمكن استخدام الأسلوب الرياضي في تحديد نقطة التعادل، وذلك على أساس الوحدات أو قيمة المبيعات المتحققة خلال فترة زمنية معينة، وعلى ضوء ذلك يمكن تحليل العلاقة ما بين التكاليف والربحية المتحققة. والمعادلة التالية توصلنا إلى تحديد نقطة التعادل على أساس المبيعات المتحققة:
        </p>
        <div className="my-4 p-3 bg-gray-100 rounded-md text-center font-mono text-base">
            <p className="font-bold">نقطة التعادل (بالمبالغ) = <span className="block border-b border-black pb-1">مجموع التكاليف الثابتة</span></p>
            <p className="mt-2">1 - <span className="block border-b border-black pb-1">مجموع التكاليف المتغيرة</span></p>
            <p className="mt-2">مجموع قيمة المبيعات</p>
        </div>
        <p>
            والناتج الذي يتم التوصل إليه من خلالها يمثل نقطة التعادل مقاسة بالمبالغ (الدينار) حيث عندها تتساوى الإيرادات والنفقات.
        </p>
        <p>
            وكذلك يمكن الوصول إلى تحديد نقطة التعادل على أساس الوحدات المباعة وعلى وفق المعادلة التالية:
        </p>
        <div className="my-4 p-3 bg-gray-100 rounded-md text-center font-mono text-base">
            <p className="font-bold">نقطة التعادل (بالوحدات) = <span className="block border-b border-black pb-1">مجموع التكاليف الثابتة</span></p>
            <p className="mt-2">سعر بيع الوحدة الواحدة - الكلف المتغيرة للوحدة الواحدة</p>
        </div>
        <p>
            ويمكن أن يوضح المثال التالي كيفية احتساب نقطة التعادل حيث توفرت البيانات التالية عن إنتاج شركة الأنوار الصناعية لإنتاج المصابيح الكهربائية للشهر السابق وهي:
        </p>
        <ul className="list-disc pr-6 space-y-1">
            <li>سعر بيع المصباح الواحد 2 دينار</li>
            <li>مجموع التكاليف الثابتة 2.000 دينار</li>
            <li>التكاليف المتغيرة للوحدة الواحدة 15 دينار</li>
            <li>حجم المبيعات المتحققة 8000 وحدة</li>
        </ul>
      </MethodCard>

      <MethodCard icon={Gem} title="2. طرق التسعير على أساس القيمة" color="border-t-green-500">
        <p>
          التسعير على أساس القيمة يستخدم تصورات المشترين للقيمة، وليس تكاليف البائع، كأساس للتسعير. هذا يعني أن الشركة لا تستطيع تصميم منتج وبرنامج تسويقي ثم تحدد السعر، بل يجب أن يكون السعر جزءاً لا يتجزأ من المزيج التسويقي قبل وضع البرنامج التسويقي.
        </p>
        <h4 className="font-bold text-lg">أ. تسعير القيمة الجيدة (Good-Value Pricing)</h4>
        <p>
          يعني تقديم المزيج الصحيح من الجودة والخدمة الجيدة بسعر عادل. في السنوات الأخيرة، تبنى العديد من المسوقين هذه الاستراتيجية، حيث قدموا إصدارات أقل تكلفة من المنتجات ذات العلامات التجارية المعروفة.
        </p>
        <h4 className="font-bold text-lg">ب. تسعير القيمة المضافة (Value-Added Pricing)</h4>
        <p>
          بدلاً من خفض الأسعار لمنافسة المنافسين، تقوم الشركات بإضافة ميزات وخدمات ذات قيمة مضافة لتمييز عروضها ودعم أسعارها المرتفعة.
        </p>
      </MethodCard>

      <MethodCard icon={Swords} title="3. طرق التسعير على أساس المنافسة" color="border-t-orange-500">
        <h4 className="font-bold text-lg">أ. التسعير على أساس الأسعار الجارية (Going-Rate Pricing)</h4>
        <p>
          تقوم الشركة بتحديد سعرها بشكل كبير بناءً على أسعار المنافسين، مع اهتمام أقل بتكاليفها أو الطلب. قد تفرض الشركة نفس السعر، أو سعراً أعلى أو أقل قليلاً من المنافسين الرئيسيين. هذه الطريقة شائعة في الأسواق التي يصعب فيها قياس التكاليف أو استجابة المنافسين.
        </p>
        <h4 className="font-bold text-lg">ب. التسعير على أساس العطاءات المختومة (Sealed-Bid Pricing)</h4>
        <p>
          يستخدم هذا النوع من التسعير عندما تقدم الشركات عطاءات للحصول على وظائف. تحدد الشركة سعرها بناءً على ما تعتقد أن المنافسين سيقدمونه، بدلاً من الاعتماد الصارم على تكاليفها أو الطلب. الهدف هو الفوز بالعقد، مما يتطلب تسعيراً أقل من المنافسين الآخرين.
        </p>
      </MethodCard>
    </div>
  );
};
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import type { Chapter } from "@/data/chapters";
// Import all chapter sections arrays
import { chapter1Sections, Chapter1 } from "@/components/chapters/Chapter1";
import { chapter2Sections, Chapter2 } from "@/components/chapters/Chapter2";
import { chapter4Sections, Chapter4 } from "@/components/chapters/Chapter4";
import { chapter5Sections, Chapter5 } from "@/components/chapters/Chapter5";
import { chapter6Sections, Chapter6 } from "@/components/chapters/Chapter6";
import { chapter7Sections, Chapter7 } from "@/components/chapters/Chapter7";
import { chapter9Sections, Chapter9 } from "@/components/chapters/Chapter9";
import { chapter11Sections, Chapter11 } from "@/components/chapters/Chapter11";
import { chapter12Sections, Chapter12 } from "@/components/chapters/Chapter12";
import { chapter13Sections, Chapter13 } from "@/components/chapters/Chapter13";
import { chapter14Sections, Chapter14 } from "@/components/chapters/Chapter14";

import { useEffect, useRef, useState, useMemo } from "react"; // Add useState, useMemo
import { useOutletContext } from "react-router-dom";
import { cn } from "@/lib/utils";
import { PresentationControls } from "@/components/PresentationControls"; // Import new component
import { DrawingCanvas } from "@/components/drawing/DrawingCanvas"; // Import DrawingCanvas

interface ChapterPageProps {
  chapter: Chapter;
}

interface OutletContext {
  setCurrentChapterTitle: (title: string | null) => void;
  isPresentationMode: boolean;
  togglePresentationMode: () => void;
}

// Map chapter IDs to their respective sections arrays
const allChapterSections: { [key: number]: { value: string; title: string; icon: React.ElementType; component: React.ReactNode }[] } = {
  1: chapter1Sections,
  2: chapter2Sections,
  4: chapter4Sections,
  5: chapter5Sections,
  6: chapter6Sections,
  7: chapter7Sections,
  9: chapter9Sections,
  11: chapter11Sections,
  12: chapter12Sections,
  13: chapter13Sections,
  14: chapter14Sections,
};

const ChapterPage = ({ chapter }: ChapterPageProps) => {
  const { setCurrentChapterTitle, isPresentationMode, togglePresentationMode } = useOutletContext<OutletContext>();
  const contentRef = useRef<HTMLDivElement>(null);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0); // New state for slide index
  const [zoomLevel, setZoomLevel] = useState(1); // New state for zoom level, default to 1 (100%)
  const [isDrawingMode, setIsDrawingMode] = useState(false); // New state for drawing mode
  const [drawingColor, setDrawingColor] = useState('#FF0000'); // Default drawing color (red)
  const [drawingStrokeWidth, setDrawingStrokeWidth] = useState(5); // Default stroke width
  const [currentCursorShape, setCurrentCursorShape] = useState('pen'); // New state for cursor shape, default to 'pen'
  const clearCanvasRef = useRef<(() => void) | null>(null); // Ref to call clearCanvas from toolbar

  const MIN_ZOOM = 0.8;
  const MAX_ZOOM = 1.5;
  const ZOOM_STEP = 0.1;

  // Get the sections for the current chapter
  const sections = useMemo(() => allChapterSections[chapter.id] || [], [chapter.id]);
  const totalSlides = sections.length;

  useEffect(() => {
    setCurrentChapterTitle(chapter.title);
    // Reset slide index, zoom level, and drawing mode when chapter changes or presentation mode is exited
    if (!isPresentationMode) {
      setCurrentSlideIndex(0);
      setZoomLevel(1); // Reset zoom when exiting presentation mode
      setIsDrawingMode(false); // Reset drawing mode
      setCurrentCursorShape('pen'); // Reset cursor shape
    }
    return () => setCurrentChapterTitle(null);
  }, [chapter.title, setCurrentChapterTitle, isPresentationMode]);

  const handleNextSlide = () => {
    setCurrentSlideIndex((prevIndex) => Math.min(prevIndex + 1, totalSlides - 1));
    if (clearCanvasRef.current) {
      clearCanvasRef.current(); // Clear drawing on slide change
    }
  };

  const handlePrevSlide = () => {
    setCurrentSlideIndex((prevIndex) => Math.max(prevIndex - 1, 0));
    if (clearCanvasRef.current) {
      clearCanvasRef.current(); // Clear drawing on slide change
    }
  };

  const handleExitPresentation = () => {
    togglePresentationMode();
    setCurrentSlideIndex(0); // Reset slide index on exit
    setZoomLevel(1); // Reset zoom on exit
    setIsDrawingMode(false); // Reset drawing mode on exit
    setCurrentCursorShape('pen'); // Reset cursor shape on exit
    if (clearCanvasRef.current) {
      clearCanvasRef.current(); // Clear drawing on exit
    }
  };

  const handleZoomIn = () => {
    setZoomLevel((prevZoom) => Math.min(prevZoom + ZOOM_STEP, MAX_ZOOM));
  };

  const handleZoomOut = () => {
    setZoomLevel((prevZoom) => Math.max(prevZoom - ZOOM_STEP, MIN_ZOOM));
  };

  const toggleDrawingMode = () => {
    setIsDrawingMode((prevMode) => !prevMode);
  };

  const handleClearCanvas = () => {
    if (clearCanvasRef.current) {
      clearCanvasRef.current();
    }
  };

  const renderChapterContent = () => {
    if (isPresentationMode) {
      // Render only the current slide's component
      if (sections[currentSlideIndex]) {
        return sections[currentSlideIndex].component;
      }
      return (
        <div className="p-8 text-center text-slate-800">
          <p>لا يوجد محتوى لهذا الشريحة.</p>
        </div>
      );
    } else {
      // Render the full chapter component (Accordion)
      switch (chapter.id) {
        case 1: return <Chapter1 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />; // Pass sections and active value
        case 2: return <Chapter2 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 4: return <Chapter4 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 5: return <Chapter5 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 6: return <Chapter6 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 7: return <Chapter7 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 9: return <Chapter9 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 11: return <Chapter11 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 12: return <Chapter12 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 13: return <Chapter13 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        case 14: return <Chapter14 key={chapter.id} sections={sections} activeSectionValue={sections[currentSlideIndex]?.value} isPresentationMode={isPresentationMode} />;
        default:
          return (
            <div className="p-8 text-center bg-gray-50 rounded-xl">
              <p className="text-gray-500"> المحتوى قيد الإنشاء... سيتم إضافة الشروحات والأدوات التفاعلية قريبًا.</p>
            </div>
          );
      }
    }
  };

  return (
    <div className={cn(
      "space-y-6",
      isPresentationMode && "fixed inset-0 z-50 flex flex-col justify-center items-center bg-white text-slate-800 overflow-auto"
    )}>
      <Card className={cn(
        "rounded-2xl shadow-soft border-0 bg-white",
        isPresentationMode && "!w-full !h-full !max-w-full !rounded-none !shadow-none !border-none !bg-transparent"
      )}>
        <CardHeader className={cn(isPresentationMode && "hidden")}>
          <CardTitle className="text-3xl font-bold text-primary-dark-blue">
            الفصل {chapter.id}: {chapter.title}
          </CardTitle>
          <CardDescription className="text-lg pt-2 text-text-gray">
            مرحبًا بك في فصل {chapter.title}. هنا ستجد شرحًا تفصيليًا وأدوات تفاعلية لمساعدتك على إتقان المفهوم.
          </CardDescription>
        </CardHeader>
        <CardContent
          ref={contentRef}
          id="chapter-content-area"
          className={cn(
            "pt-2",
            isPresentationMode && "w-full h-full overflow-auto !p-8 text-slate-800 text-2xl leading-loose bg-white rounded-none"
          )}
          style={isPresentationMode ? { transform: `scale(${zoomLevel})`, transformOrigin: 'center' } : {}} // Apply zoom here
        >
          {renderChapterContent()}
        </CardContent>
      </Card>

      {isPresentationMode && totalSlides > 0 && (
        <>
          <DrawingCanvas
            color={drawingColor}
            strokeWidth={drawingStrokeWidth}
            isDrawingMode={isDrawingMode}
            zoomLevel={zoomLevel}
            onClearRef={clearCanvasRef}
            currentCursorShape={currentCursorShape} // Pass current cursor shape
          />
          {/* DrawingToolbar is now integrated into PresentationControls */}
          <PresentationControls
            currentSlideIndex={currentSlideIndex}
            totalSlides={totalSlides}
            onNext={handleNextSlide}
            onPrev={handlePrevSlide}
            onExit={handleExitPresentation}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            isZoomInDisabled={zoomLevel >= MAX_ZOOM}
            isZoomOutDisabled={zoomLevel <= MIN_ZOOM}
            // Props for drawing functionality
            currentColor={drawingColor}
            setCurrentColor={setDrawingColor}
            currentStrokeWidth={drawingStrokeWidth}
            setCurrentStrokeWidth={setDrawingStrokeWidth}
            isDrawingMode={isDrawingMode}
            toggleDrawingMode={toggleDrawingMode}
            onClearCanvas={handleClearCanvas}
            currentCursorShape={currentCursorShape}
            setCurrentCursorShape={setCurrentCursorShape}
          />
        </>
      )}
    </div>
  );
};

export default ChapterPage;
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { ArrowRight, ArrowDown } from 'lucide-react';

const ServiceChannelDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (13-6): منافذ توزيع الخدمات</h4>
        <div className="flex flex-col items-center">
            <div className="grid grid-cols-3 gap-4 w-full max-w-2xl">
                {/* Column 1: Manufacturer */}
                <div className="flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">مجهز<br/>(منتج الخدمة)</div>
                </div>
                {/* Column 2: Agent */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">الوكيل</div>
                </div>
                {/* Column 3: Consumer/Industrial User */}
                <div className="flex flex-col items-center">
                    <div className="bg-white p-3 rounded-md shadow border text-center font-medium text-slate-700">المستهلك أو المستخدم الصناعي<br/>(منظمات الأعمال)</div>
                </div>
            </div>
            {/* Arrows and Labels */}
            <div className="relative w-full max-w-2xl h-24 mt-[-1rem]">
                {/* Channel 0 */}
                <div className="absolute top-0 right-[calc(66.66%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-0 right-[calc(66.66%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute top-0 right-[calc(66.66%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">0</span>
                <div className="absolute top-0 left-[calc(33.33%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute top-0 left-[calc(33.33%-1.5rem)] -translate-x-1/2 text-slate-400" />

                {/* Channel 1 */}
                <div className="absolute bottom-0 right-[calc(66.66%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute bottom-0 right-[calc(66.66%-1.5rem)] translate-x-1/2 text-slate-400" />
                <span className="absolute bottom-0 right-[calc(66.66%-1.5rem)] -translate-x-full -mt-6 text-xs text-slate-600">1</span>
                <div className="absolute bottom-0 left-[calc(33.33%-1.5rem)] w-px h-full bg-slate-400"></div>
                <ArrowRight className="absolute bottom-0 left-[calc(33.33%-1.5rem)] -translate-x-1/2 text-slate-400" />
            </div>
        </div>
        <p className="text-center text-xs text-slate-500 mt-6">المصدر: Kotler & Keller, Marketing Management, 2016, p.587</p>
    </div>
);

const ChannelDescription = ({ number, title, children }: { number: number; title: string; children: React.ReactNode }) => (
    <div className="p-6 bg-white rounded-lg border border-slate-200 shadow-sm space-y-4">
        <h4 className="font-bold text-xl text-indigo-800">{number}. {title}</h4>
        <p className="text-slate-700 leading-relaxed">{children}</p>
    </div>
);

export const Chapter13ServiceChannels = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800">
            منافذ تسويق الخدمات (Services Marketing Channels)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            كما هو الحال بالنسبة للسلع الاستهلاكية والإنتاجية بوجود منافذ تسويقية لها، فإن الخدمات لها أيضاً منافذ تسويقية يمكن اعتمادها في سبيل إيصال الخدمات إلى المشتري. ولكن لا تأخذ هذه المنافذ نفس الشكل أو العدد لما هو عليه بالنسبة للسلع الاستهلاكية أو الإنتاجية ولعل الصفة الغالبة لمنافذ توزيع الخدمات هي كونها مباشرة، أي أنها تقدم من قبل بائع الخدمة، ومن الصعب أن تعطى إلى شخص وسيط. ولكن هذا لا يمنع من القول بأن بعض الخدمات يمكن أن تمنح إلى آخرين ليقوموا بتأديتها أو إيصالها لمن يطلبها وكما يوضح فيه الشكل (13-6) وهي:
          </p>
        </CardContent>
      </Card>

      <ServiceChannelDiagram />

      <div className="space-y-8 pt-6 border-t">
        <ChannelDescription
          number={1}
          title="المنفذ الصفري (المباشر) Zero – level Channel (Direct)"
        >
          هو المنفذ الشائع الاستخدام عندما يتم تقديم الخدمة من قبل منتجها ذاته، كما هو الحال مثلاً بالنسبة لخدمات الطبيب، أو مصلح الأجهزة الكهربائية، أو مصلح السيارة. حيث في هذه الأمثلة البسيطة يتضح أن الطبيب لا يمكن أن يحول شخص آخر في معالجة المريض، إلا إذا كان مستوعباً لمهنة الطب وكذلك الحال لبقية الخدمات الأخرى.
        </ChannelDescription>

        <ChannelDescription
          number={2}
          title="المنفذ الأحادي One-level Channel"
        >
          هو المنفذ الذي يقوم على حلقة وسيطة بين مجهز (منتج الخدمة) ومشتريها. وقد تكون هذه الحلقة على شكل منظمة أو مجموعة أشخاص ينتمون إلى جهة معينة أو مستقلين بنشاطهم التجاري عن أي طرف آخر، ويكونون مخولين أو يمتلكون الصلاحية في تأدية الخدمة والتوسط في تقديمها لمن يطلبها. كما يمكن أن يفتح المنتج ووكالات متخصصة بالاتفاق مع أطراف معينة لتقديم الخدمة بحيث يتاح للمستهلك أو مستعمل الخدمة (منظمة الأعمال) من شراء الخدمة ومن عدة أماكن. ومن أبرز الأمثلة على مثل هذا النوع من الوحدات في المنفذ الأحادي هي مكاتب خدمات النقل ووكلاء الطيران ومكاتب تسويق خدمة التأمين، ومكاتب السياحة... إلخ. وبعامة تمتاز منافذ توزيع الخدمات بكونها محدودة ومقتصرة في تأدية نشاطها على مناطق جغرافية معينة قد تصغر أو تكبر تبعاً لنوع الخدمة المقدمة. كما أن الخدمة تمتاز بكونها غير ملموسة، فلذلك لا يمكن خزنها، بل تقدم عند طلبها من قبل المشتري.
        </ChannelDescription>
      </div>
    </div>
  );
};
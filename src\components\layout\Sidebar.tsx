import { NavLink } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, Toolt<PERSON><PERSON>rigger, TooltipProvider } from "@/components/ui/tooltip";
import { chapters } from "@/data/chapters";
import { cn } from "@/lib/utils";
// No longer need useOutletContext here as prop is passed directly

interface SidebarProps {
  isPresentationMode: boolean;
}

export const Sidebar = ({ isPresentationMode }: SidebarProps) => {
  // isPresentationMode is now received as a prop
  return (
    <aside className={`h-screen sticky top-0 bg-white p-2 flex flex-col overflow-y-auto ${isPresentationMode ? 'hidden' : ''}`}>
      <nav className="flex-grow mt-4">
        <ul className="space-y-1">
          {chapters.map((chapter) => (
            <li key={chapter.id}>
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <NavLink
                      to={chapter.path}
                      className={({ isActive }) =>
                        cn(
                          "flex items-center w-full p-3 text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors",
                          isActive && "bg-blue-100 text-blue-800 font-bold"
                        )
                      }
                    >
                      <chapter.icon className="h-5 w-5 ml-3 flex-shrink-0" />
                      <span className="truncate">{chapter.shortTitle}</span>
                    </NavLink>
                  </TooltipTrigger>
                  <TooltipContent side="left" className="font-sans bg-gray-800 text-white rounded-md">
                    <p>{chapter.title}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};
import React from 'react';
import { ArrowL<PERSON>t, MessageSquare, Send, Code, Tv, User, UserCheck, VolumeX, Repeat, ArrowDown } from 'lucide-react';

const CommunicationProcessDiagram = () => (
    <div dir="rtl" className="my-8 p-4 sm:p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (11-2): عناصر نظام الاتصالات التسويقية</h4>
        <div className="relative p-6 border-2 border-dashed border-slate-400 rounded-lg">
            <div className="absolute -top-3.5 left-1/2 -translate-x-1/2 bg-slate-50 px-2 text-slate-600 font-semibold">الضوضاء</div>
            
            <div className="flex flex-col md:flex-row-reverse items-center justify-center gap-2">
                <div className="p-3 rounded-md shadow border text-center w-full md:w-32 bg-white">المرسل</div>
                <ArrowLeft className="h-6 w-6 text-slate-500 transform rotate-90 md:rotate-0" />
                <div className="p-3 rounded-md shadow border text-center w-full md:w-32 bg-white">الترميز</div>
                <ArrowLeft className="h-6 w-6 text-slate-500 transform rotate-90 md:rotate-0" />
                <div className="p-3 rounded-md shadow border text-center w-full md:w-32 bg-red-100 border-red-300 text-red-800">الرسالة / الوسيلة</div>
                <ArrowLeft className="h-6 w-6 text-slate-500 transform rotate-90 md:rotate-0" />
                <div className="p-3 rounded-md shadow border text-center w-full md:w-32 bg-white">التفسير</div>
                <ArrowLeft className="h-6 w-6 text-slate-500 transform rotate-90 md:rotate-0" />
                <div className="p-3 rounded-md shadow border text-center w-full md:w-32 bg-white">المستلم</div>
            </div>

            <div className="mt-6 flex flex-col md:flex-row-reverse justify-between items-center gap-4">
                <div className="flex flex-col items-center gap-2">
                    <ArrowDown className="h-5 w-5 text-slate-400" />
                    <div className="p-3 rounded-md shadow border text-center w-32 bg-green-100 border-green-300 text-green-800">التغذية العكسية</div>
                </div>
                <div className="flex-1 border-t-2 border-dashed border-slate-400 mt-8 hidden md:block relative">
                    <ArrowLeft className="absolute top-1/2 left-0 -translate-y-1/2 h-5 w-5 text-slate-400" />
                </div>
                <div className="flex flex-col items-center gap-2">
                    <ArrowDown className="h-5 w-5 text-slate-400" />
                    <div className="p-3 rounded-md shadow border text-center w-32 bg-green-100 border-green-300 text-green-800">الاستجابة</div>
                </div>
            </div>
        </div>
    </div>
);

const ElementCard = ({ number, title, icon: Icon, children }: { number: number; title: string; icon: React.ElementType; children: React.ReactNode }) => (
    <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
        <h5 className="font-bold text-lg text-slate-800 flex items-center gap-3">
            <span className="flex-shrink-0 h-8 w-8 bg-red-100 text-red-700 font-bold rounded-full flex items-center justify-center">
                {number}
            </span>
            <Icon className="h-5 w-5 text-red-600" />
            <span>{title}</span>
        </h5>
        <div className="text-slate-600 mt-2 leading-relaxed pr-12 space-y-2">{children}</div>
    </div>
);

export const Chapter11CommunicationProcess = () => {
  return (
    <div className="space-y-6">
      <p className="text-slate-700 leading-relaxed">
        تشكل الاتصالات التسويقية الجزء الأكبر والمهم في مجمل عمليات الاتصال التي تقوم بها منظمة الأعمال، وتحديداً في تفاعلها مع مفردات البيئة الخارجية المحيطة بها. إذ أن الاتصالات التسويقية تسعى على وفق هذا التوجه إلى تحقيق أهداف المنظمة الاستراتيجية والمتمثلة ببلوغ مستوى المبيعات والحصة السوقية المقررة، تحسين وارتفاع مستوى الأرباح، زيادة مكانة وقوة المنظمة في السوق... إلخ.
      </p>
      <p className="text-slate-700 leading-relaxed">
        ومن الواضح والمناسب التأكيد على أن الاتصالات التسويقية في جوهرها اتصالات هادفة ومصممة مسبقاً، وليست حالة عرضية واجتهادية من قبل القائمين عليها أو المنفذين لها. لأنها أساساً تقوم على خلق التأثير والإقناع والتذكير بمضمون ومحتوى الرسالة المستهدفة للجمهور والعمل على تعزيز العلاقة ما بين الطرفين.
      </p>
      <p className="text-slate-700 leading-relaxed">
        ولكي تحقق الاتصالات التسويقية تأثيرها المطلوب بالجمهور أو الطرف المستهدف، فإن المسوقين عليهم أن يفهموا بشكل واضح أسس وعناصر نظام عملية الاتصالات التسويقية. لكي تحقق الاتصال الفاعل المطلوب، ويجب عدم النظر إلى كون الاتصالات التسويقية تنحصر في حدود نقل المعلومة فقط، بل أنها تمثل اشتراك متفاعل ما بين الطرفين وتحقيق فهم مشترك لمضمون وأبعاد ورموز الرسالة المستخدمة من عبر وسيلة الاتصال المعتمدة في تحقيق ذلك الاتصال. والشكل (11-2) يوضح عناصر نظام الاتصالات التسويقية وهي:
      </p>
      
      <CommunicationProcessDiagram />

      <div className="space-y-4 pt-6 border-t">
        <ElementCard number={1} title="المرسل" icon={Send}>
            <p>أي طرف في المنظمة قادر على إرسال معلومة إلى الآخرين ولا تتم عملية الإرسال دون وجود هدف من وراء عملية الاتصال. فكأن تكون لغرض الإخبار عن وجود معلومة معينة أو إنجاز عمل أو رد فعل لطلب سابق.. إلخ وبالتالي فإن المرسل هو نقطة البدء في عملية الاتصال.</p>
        </ElementCard>
        <ElementCard number={2} title="الترميز" icon={Code}>
            <p>وهي مجموعة من المعاني المحددة في عملية الاتصال والتي تستخدم لتحديد فهم مشترك عند إيصال الرسالة واستلامها، وقد تتضمن هذه الرموز هي لغة، إشارات، كتابة، حروف.. إلخ بحيث يكون فهمها مقبولاً من الطرفين.</p>
        </ElementCard>
        <ElementCard number={3} title="الرسالة" icon={MessageSquare}>
            <p>هي مضمون ومحتوى عملية الاتصال، إذ لولا وجود رسالة يتم تبادلها ما بين الطرفين لما استوجبت عملية الاتصال.</p>
        </ElementCard>
        <ElementCard number={4} title="الوسيلة" icon={Tv}>
            <p>وهي الأداة أو التقنية التي تم استخدامها في إنجاز عملية الاتصال وسواء كانت داخل المنظمة أو خارجها، وسواء كانت فردية أو جماعية، فضلاً عن كونها مكتوبة أو منطوقة أو مشاهدة.</p>
        </ElementCard>
        <ElementCard number={5} title="التفسير" icon={UserCheck}>
            <p>وهي التوافق بين الرموز التي تم إرسالها مع قدرة المستلم على التفسير لها. فكأن تكون مثلاً إرسال الرسالة بلغة أجنبية لا يستطيع المستلم من فهمها لأنه لا يعرف أبعاد رموز الرسالة فتكون قد فشلت عملية الاتصال.</p>
        </ElementCard>
        <ElementCard number={6} title="المستلم" icon={User}>
            <p>وهي قدرة الفرد على إدراك الرسالة عبر حواسه الخمسة (السمع، البصر، الشم، اللمس، والتذوق) ومعرفة مضمون الرسالة. ومن المناسب القول هنا بأن المدير عندما يستلم معلومة ولا يعرف من أين مصدرها، فإنه يرى بأن هذه الرسالة لا تعني له شيئاً ولا يستجيب أو يتفاعل معها. وهذه الوسيلة لا تحدد بها أي موقف أو فعل أو كلام أو حدث.. إلخ يحصل أمامه خلال اليوم.</p>
        </ElementCard>
        <ElementCard number={7} title="الاستجابة" icon={UserCheck}>
            <p>وهي تمثل قبول أو رفض الرسالة ويتوقف هذا الأمر على حاجة المستلم، وقيمه وتقاليده والأعراف، والأثر في التنفيذ.. إلخ.</p>
        </ElementCard>
        <ElementCard number={8} title="التغذية العكسية" icon={Repeat}>
            <p>وهي تعبير عن مقدار الفهم الصحيح للرسالة من عدمه والمشير مستوى ومقدار رد الفعل المتحقق لدى المستلم عند إعادة اتصاله مع المرسل أو عدمه.</p>
        </ElementCard>
        <ElementCard number={9} title="الضوضاء" icon={VolumeX}>
            <p>وهي مجموعة المؤثرات الخلفية والتي قد تكون مقصودة أو غير مقصودة، وهي من شأنها أن تؤثر على عملية الاتصال في بعض مراحلها السابق ذكرها، والتي يكون لها أثر سلبي في تحقيق فاعلية الاتصالات.</p>
            <p>وعليه فإنه لتحقيق فاعلية الاتصالات التسويقية وإنجاز النشاط الترويجي بالشكل الصحيح فإن الأمر يتطلب الآتي:</p>
            <ul className="list-disc pr-6 space-y-1">
                <li>تحديد الأفراد والجهات المستفيدة والتي ترغب المنظمة في الاتصال بهم بشكل دقيق وواضح.</li>
                <li>تحديد الهدف من وراء عملية الاتصال، والتي قد تكون لغرض المعرفة، التعليم، الإقناع، التحفيز، الانتباه.. إلخ.</li>
                <li>الدقة في تصميم الرسالة من حيث المحتوى والهيكل والمضمون.</li>
                <li>الاختيار السليم لقناة الاتصال والتي قد تكون شخصية أو غير شخصية.</li>
                <li>وإقرار المصدر الموثوق للرسالة التي ستعطي مصداقية في عملية الاتصال.</li>
                <li>تحديد خصائص الجمهور المستهدف وكيفية التعامل مع كل طرف من الأطراف.</li>
                <li>قياس النتائج المتحققة من عملية الاتصال والمشير مدى الكفاءة في الأداء ومن خلال الاستجابة ورد الفعل المتحقق.</li>
            </ul>
            <p>وخلاصة الموضوع يمكن الإشارة في القول بأن هذه العناصر تمثل نظام الاتصالات التسويقية، في تحول دون أي لبس أو تماسي في عملية الاتصال سلباً أو إيجاباً للوصول إلى الهدف المطلوب من عملية الاتصال.</p>
        </ElementCard>
      </div>
    </div>
  );
};
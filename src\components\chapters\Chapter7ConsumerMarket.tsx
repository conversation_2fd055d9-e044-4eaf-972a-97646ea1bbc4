import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Gem, ShoppingBasket, Zap } from 'lucide-react';

const BulletPoint = ({ children }: { children: React.ReactNode }) => (
    <li className="flex items-start gap-3">
        <span className="h-2 w-2 bg-slate-400 rounded-full mt-2 flex-shrink-0"></span>
        <span className="text-text-gray">{children}</span>
    </li>
);

const GoodTypeCard = ({ icon: Icon, title, enTitle, definition, characteristics, color }: { icon: React.ElementType, title: string, enTitle: string, definition: string, characteristics: string[], color: string }) => (
    <Card className={`h-full flex flex-col border-t-4 ${color}`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-xl text-dark-gray">
                <Icon className={`h-7 w-7 ${color.replace('border-', 'text-')}`} />
                {title}
            </CardTitle>
            <p className="text-sm text-slate-500 -mt-2">{enTitle}</p>
        </CardHeader>
        <CardContent className="flex-grow flex flex-col">
            <p className="text-text-gray mb-4 italic">"{definition}"</p>
            <div className="flex-grow">
                <h5 className="font-semibold mb-2 text-dark-gray">الخصائص:</h5>
                <ul className="space-y-2 text-sm">
                    {characteristics.map((char, index) => (
                        <li key={index} className="flex items-start gap-2">
                            <span className="font-mono text-xs text-slate-500 mt-1">{String.fromCharCode(97 + index)}.</span>
                            <span className="text-text-gray">{char}</span>
                        </li>
                    ))}
                </ul>
            </div>
        </CardContent>
    </Card>
);

export const Chapter7ConsumerMarket = () => {
  return (
    <div className="space-y-8">
      <Card className="bg-blue-50/30 border-blue-200">
        <CardHeader>
          <CardTitle className="text-2xl text-secondary-blue">سوق المستهلك (Consumer Market)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-text-gray leading-relaxed">
          <p>
            يقصد بسوق المستهلك بأنها تلك السوق التي تحتوي على جميع المستهلكين النهائيين للسلع والتي تتعامل مع السلع المتعلقة بالاستهلاك النهائي. وللإشارة في التوضيح بأنه السوق الاستهلاكية الامريكية تحتوي على اكثر من 323 مليون شخص، ويستهلكون من السلع والخدمات سنويا ما يقرب من 11.9 ترليون دولار، وبذلك تعد هذه السوق من اكثر الاسواق في العالمية جاذبية ومحفزة للعمل فيها.
          </p>
          <p>
            وبطبيعة الحال من الممكن ان تكون هذه السلع الموجودة في السوق بنماذج مختلفة، كأن تكون سلع معمرة (وهي تلك السلع التي يمكن الانتفاع منها لفترة طويلة مثل التلفزيون، الثلاجة، الأثاث)، أو غير معمرة (وهي تلك التي يتم استهلاكها في فترة قصيرة جداً مثل الطعام، المشروبات، الملابس).
          </p>
          <div>
            <h4 className="font-semibold mb-2 text-dark-gray">في الغالب تتصف السلع الاستهلاكية بالآتي:</h4>
            <ul className="space-y-2 pr-4">
              <BulletPoint>أثمانها في الغالب تكون غير مرتفعة قياساً بما هو عليه في البضائع الصناعية.</BulletPoint>
              <BulletPoint>باعتها منتشرون بشكل واسع وكبير.</BulletPoint>
              <BulletPoint>تكرار صفقات الشراء وبشكل دوري وخصوصاً للسلع غير المعمرة.</BulletPoint>
              <BulletPoint>قرار الشراء المتخذ من قبل المشتري في الغالب يكون بصفة عاطفية أكثر مما هو عقلاني.</BulletPoint>
              <BulletPoint>كمية البضاعة المشتراة قليلة نسبياً في المرة الواحدة.</BulletPoint>
              <BulletPoint>قابلة للتلف والتقادم بشكل سريع.</BulletPoint>
            </ul>
          </div>
          <p className="font-semibold pt-2 border-t mt-4 text-text-gray">
            وللحقيقة نقول بأن هذه الصفات قد لا تنطبق على جميع السلع الاستهلاكية، لكون حجم ومدى السلع الاستهلاكية كبير ويحتوي على اعداد وأنواع مختلفة ومتباينة. وقد تنطبق تفصيلاً على البعض منها وقد تختلف جزئياً عن البعض الآخر.
          </p>
        </CardContent>
      </Card>

      <div className="pt-8 mt-8 border-t">
        <h3 className="text-2xl font-bold text-center mb-6 text-dark-gray">أنواع السلع الاستهلاكية</h3>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <GoodTypeCard
            icon={Zap}
            title="1. السلع الميسرة"
            enTitle="Convenience Goods"
            color="border-green-500"
            definition="هي السلع التي لا يبذل المستهلك في سبيلها جهداً كبيراً في الحصول عليها، لأنها متيسرة ومتاحة بشكل كبير وواضح."
            characteristics={[
              "أثمانها زهيدة جداً.",
              "باعتها منتشرون بشكل كبير جداً، لذلك لا تتطلب جهداً في الحصول عليها.",
              "تتكرر عملية شرائها وربما لأكثر من مرة في اليوم الواحد.",
              "لا تحتاج إلى تفكير معمق عند الشراء، لأن ثمنها منخفض وليس بذا تأثير على دخل المشتري.",
              "هامش الربح قليل في الوحدة الواحدة لانخفاض ثمنها بالأساس.",
              "يمكن استخدام أكثر من منفذ توزيعي لغرض إيصالها للمستهلك وغالباً ما يتم استخدام المنافذ غير المباشرة.",
              "الحملة الإعلانية للبضاعة وتكاليفها يتحملها المنتج لأن هامش الربح البسيط الذي يحصل عليه البائع لا يبرر قيامه بالإعلان."
            ]}
          />
          <GoodTypeCard
            icon={ShoppingBasket}
            title="2. سلع التسوق"
            enTitle="Shopping Goods"
            color="border-orange-500"
            definition="هي تلك السلع التي يشتريها المستهلك بعد إجراء سلسلة من المقارنات من حيث السعر والنوعية مع ما بين عدد من المتاجر."
            characteristics={[
              "أثمانها مرتفعة نسبياً قياساً لما هو عليه بالنسبة للسلع الميسرة.",
              "باعتها غالباً ما يكونون مجتمعين في مناطق معينة ومتاجرهم متقاربة وهم قليلون نسبياً.",
              "يجري المستهلك مقارنة أكثر بين المتاجر للوقوف على أفضل مواصفات وأسعار.",
              "تحتاج إلى تفكير أطول نسبياً عند عملية الشراء لأن أثمانها غالباً ما تؤثر على دخل المستهلك.",
              "هامش الربح فيها للوحدة يكون مناسب وجيد وهو أكثر مما عليه بالنسبة للسلع الميسرة.",
              "يمكن استخدام أكثر من منفذ توزيعي لإيصال السلع إلى المستهلك الأخير.",
              "عمليات شراء هذا البضائع قد لا تتكرر بشكل مستمر وربما تتباعد بين فترات زمنية تطول أو تقتصر نسبياً."
            ]}
          />
          <GoodTypeCard
            icon={Gem}
            title="3. السلع الخاصة"
            enTitle="Specialty Goods"
            color="border-purple-500"
            definition="وهي تلك البضائع التي يبذل المشتري جهداً كبيراً في سبيل الحصول عليها، وقد تأخذ المقارنة جهداً واضحاً عند شراء هذه الأنواع."
            characteristics={[
              "أسعارها في الغالب مرتفعة الثمن.",
              "باعتها منفردين في مناطق بيعية معينة، أي انهم قليلون نسبياً وحتى بالمقارنة مع باعة سلع التسوق.",
              "يبذل المنتج جهداً واضحاً في اختيار الباعة (المتجر) لأن البضاعة تحتاج إلى مواصفات مناسبة للبائع وقدرة على إنجاح السلعة في السوق.",
              "لا تتكرر عملية الشراء إلا بفترات زمنية متباعدة، وقد تكون طويلة جداً وتتجاوز السنوات.",
              "هامش الربح في الوحدة الواحدة عالي نسبياً ولصالح البائع.",
              "غالباً ما يتفق البائع والمنتج على تصميم وتنفيذ الحملة الإعلانية، وما يترتب عليها من تكاليف يتحملها بنسبة معينة لكل واحد منهم.",
              "تستخدم أقصر الطرق في المنافذ التوزيعية لمثل هذه البضائع."
            ]}
          />
        </div>
        <Card className="mt-6 bg-slate-50/50">
            <CardContent className="p-4 text-sm text-text-gray">
                <p><strong className="text-dark-gray">ملاحظة هامة:</strong> خاتمة القول يمكن إشارة السؤال الذي جوهره فيما إذا كان هذا التصنيف بالإمكان تطبيقه على جميع المستهلكين؟ الجواب هو بالطبع كلا... حيث أن بعض السلع التي تعتبر بمثابة سلع تسوق كما هي بالنسبة للملابس لدى البعض تعتبر سلع ميسرة لدى البعض الآخر. ولعل هذا التباين يعود بالأساس على عامل مهم جداً هو الدخل الذي يحصل عليه الفرد لإشباع حاجاته من السلع، فمن كان دخله قليل فإن إشباع حاجاته من السلع سيكون محدد، والعكس صحيح.</p>
            </CardContent>
        </Card>
      </div>
    </div>
  );
};
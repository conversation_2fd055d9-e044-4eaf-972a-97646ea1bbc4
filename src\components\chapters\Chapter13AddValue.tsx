import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>R<PERSON>, ArrowLeft } from 'lucide-react';

const Definition = ({ text, source }: { text: string, source?: string }) => (
  <div className="p-4 my-4 border-r-4 border-indigo-600 rounded-lg bg-indigo-50/50 shadow-sm">
    {source && <h4 className="font-bold text-indigo-800 mb-2">{source}</h4>}
    <blockquote className="text-slate-700 leading-relaxed italic">
      "{text}"
    </blockquote>
  </div>
);

const ValueAddDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border overflow-x-auto">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (13-3): مجالات القيمة المضافة المتحققة في القناة التسويقية</h4>
        <div className="flex items-start justify-center gap-4 min-w-[700px]">
            {/* Left Column: Consumers */}
            <div className="flex flex-col items-center gap-2">
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">الزبون (المشتري)</div>
            </div>

            {/* Arrows to Retailers */}
            <div className="flex flex-col items-center justify-center h-full gap-2">
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
            </div>

            {/* Middle Column: Retailers */}
            <div className="flex flex-col items-center gap-2">
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">تجار التجزئة</div>
            </div>

            {/* Arrows to Wholesalers */}
            <div className="flex flex-col items-center justify-center h-full gap-2">
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
            </div>

            {/* Right Column: Wholesalers */}
            <div className="flex flex-col items-center gap-2">
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">تجار الجملة</div>
            </div>

            {/* Arrows to Manufacturers */}
            <div className="flex flex-col items-center justify-center h-full gap-2">
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
                <ArrowLeft className="h-5 w-5 text-slate-500" />
            </div>

            {/* Far Right Column: Manufacturers */}
            <div className="flex flex-col items-center gap-2">
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">المنتجون (المصنعون)</div>
            </div>
        </div>
        <div className="flex justify-around mt-4 text-sm font-semibold text-slate-700">
            <span>التملك</span>
            <span>الترويج</span>
            <span>التفاوض</span>
            <span>التمويل</span>
            <span>المخاطرة</span>
            <span>الطلب</span>
            <span>المعلومات</span>
        </div>
    </div>
);

const FunctionItem = ({ number, title, enTitle, children }: { number: number; title: string; enTitle: string; children: React.ReactNode }) => (
    <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
        <h5 className="font-bold text-lg text-indigo-800 mb-2">{number}. {title} <span className="text-sm font-normal text-slate-500">({enTitle})</span></h5>
        <p className="text-slate-700 leading-relaxed text-sm">{children}</p>
    </div>
);

export const Chapter13AddValue = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800">
            القيمة المضافة للقناة التسويقية (M.C. Add Value)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            تهتم القناة التسويقية بشكل أساسي في عملية تحريك البضائع من المصنع إلى المشتري بالوقت والمكان والحكمة المناسبة وبما لا يحدث هناك فجوة بين تقديم الطلب والاستجابة لذلك. ولكن للحقيقة فإن القناة التسويقية لا تنحصر في هذا الجانب فحسب، بل تقوم بتحقيق قيمة مضافة للسلع التي يتم توزيعها وهي كافة العمليات المتعلقة بخزن ونقل وتحريك المنتجات المادية النهائية والمواد الأولية داخل القناة التسويقية وباتجاه وصولها إلى المشتري النهائي.
          </p>
          <p className="font-semibold pt-4 border-t">
            وللأطراف المختلفة ذات العلاقة بالمنشأة، وبشكل عام يمكن حصر المجالات أو المهام التي يتم من خلالها تحقيق هذه القيمة المضافة عبر الآتي (4) والتي يمكن إجمالها بالشكل (13-3) وهي:
          </p>
        </CardContent>
      </Card>

      <ValueAddDiagram />

      <div className="space-y-4 pt-6 border-t">
        <FunctionItem number={1} title="التملك المادي" enTitle="Physical Possession">
          وهي كافة العمليات المتعلقة بخزن ونقل وتحريك المنتجات المادية النهائية والمواد الأولية داخل القناة التسويقية وباتجاه وصولها إلى المشتري النهائي.
        </FunctionItem>
        <FunctionItem number={2} title="الترويج" enTitle="Promotion">
          ممارسة الأنشطة الترويجية المتوافقة مع خصوصية المنتج الذي يتم توزيعه عبر القناة، وما يمتلكه الوسيط من قدرات وإمكانيات ترويجية قد ينوب في استخدامها بدلاً عن المنتج في الترويج للبضاعة في الأسواق التي يعمل بها، وبخاصة عندما تكون تلك الأسواق دولية وخارج بلد المنتج.
        </FunctionItem>
        <FunctionItem number={3} title="التفاوض" enTitle="Negotiation">
          مجمل العمليات التي تحصل ما بين أعضاء القناة بهدف الاتفاق النهائي حول شروط الانتقال المادي أو التملك ما بينهم حتى وصولها إلى المستهلك النهائي وبعد الناتج المتحقق من عملية التفاوض بمثابة عقد ملزم للأطراف المتفق عليه وبما يخدم مصالحهم المشتركة والحفاظ على سمعة ومكانة السلعة في السوق.
        </FunctionItem>
        <FunctionItem number={4} title="التمويل" enTitle="Financing">
          تتعلق بكافة الجوانب المالية ذات العلاقة مع القناة التسويقية والتي تنصب أساساً على قيمة الاستثمار المتحقق في المخزون الموجود لدى أعضاء القناة، ومدى تأثيره على المركز المالي للمنظمة في حالة البطئ في دورانه، فضلاً عن ارتباط ذلك بالسماحات التي تقدمها على أساس الشراء بالكميات الكبيرة، أو الشراء النقدي أو الآجل، وعن الطرق المتعددة في التسديد وفترات ذلك.
        </FunctionItem>
        <FunctionItem number={5} title="الخطر" enTitle="Risk">
          بسبب الإهمال واحتمالات تعرض المواد إلى الفساد أو التلف جراء الخزن لدى الوسطاء والتي هي بحوزته وليس من ضمن ملكيته، فإن ذلك يعد من الأخطار الكبيرة التي ستنعكس على المنتجات التي تتعامل بها المنظمة والتي قد تنعكس على سمعتها إذا ما وصلت إلى المستهلك بصورة مخالفة إلى المواصفات المثبتة عليها أو التي أنتجت من أجلها. لذلك تعد مسألة الخطر المحتمل حصوله في القناة التسويقية من الوظائف المهمة التي تسعى إلى تحقيقها المنظمة عند تعاملها مع أعضاء القناة لكي لا يحدث ما يؤثر سلباً على المنتجات التي تتعامل بها.
        </FunctionItem>
        <FunctionItem number={6} title="الطلب" enTitle="Ordering">
          تتمثل بعمليات الشراء التي تحصل في القناة الموجهة نحو إشباع حاجات المستهلك أو تنظيم عملية تدفق هذه المنتجات في فترات زمنية محدودة (فصلي، شهري، أسبوعي، أو أجزاء الأسبوع) وبما يؤول إلى تحقيق تدفق مستمر إلى السوق وعدم حدوث شحه أو فقدان البضاعة، لأن ذلك سينعكس سلباً على سمعة المنتج واحتمالية دخول منتجات بديلة لذلك المنتج.
        </FunctionItem>
        <FunctionItem number={7} title="المعلومات" enTitle="Information">
          تنصب المعلومات هنا على ما يتعلق باستمرار تدفق المنتجات إلى السوق وعبر القناة التسويقية المعتمدة وذلك باتجاه تعظيم الفرص التسويقية التي تسعى لتحقيقها المنظمة. وهذه المعلومات تتعلق بشكل خاص بحالة الأسعار السائدة في السوق، حالة المنافسة لإقرار شكل وخصوصية الوسطاء الذين يتم إعتمادهم في القناة، وجودة المنتج وقدرة الوسطاء على تعزيز مكانته في السوق... إلخ.
        </FunctionItem>
      </div>
      <p className="text-slate-700 leading-relaxed pt-4 border-t mt-6">
        ويلاحظ من الشكل الاختلاف في اتجاهات الوظائف حيث يبدأ البعض منها من المصنعين باتجاه المشتري وكما هو حاصل في وظائف التملك المادي، الترويج، والآخر من الوظائف تأخذ الاتجاه المعاكس كما هو في التمويل (استحصال النقد) والمخاطر، وطلب الحصول على البضاعة. والبعض الآخر منها يكون باتجاهين متضادين في التفاعل كما هو في تدفق المعلومات واستحصالها والتفاوض. وهذه الأشكال في الوظائف تدل على عمق وتعدد الأداء في القناة التسويقية ومستوى وأهمية العمل الذي ينجز من خلالها ودورها في المزيج التسويقي للمنظمة.
      </p>
    </div>
  );
};
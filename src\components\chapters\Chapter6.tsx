import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter6Flowchart } from './Chapter6Flowchart';
import { Chapter6Introduction } from './Chapter6Introduction';
import { Chapter6Benefits } from './Chapter6Benefits';
import { Chapter6MIS } from './Chapter6MIS';
import { Chapter6ResearchProcess } from './Chapter6ResearchProcess';
import { BookOpen, Share2, Database, Search, Sparkles, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 6
const chapter6Quiz = quizzes.find(q => q.chapterId === 6);

// Define sections for Chapter 6
export const chapter6Sections = [
  { value: "item-1", title: "هيكلية الفصل", icon: Share2, component: <Chapter6Flowchart /> },
  { value: "item-2", title: "مفهوم نظام المعلومات التسويقية", icon: BookOpen, component: <Chapter6Introduction /> },
  { value: "item-3", title: "منافع نظام المعلومات التسويقي", icon: Sparkles, component: <Chapter6Benefits /> },
  { value: "item-4", title: "مكونات نظام المعلومات التسويقية", icon: Database, component: <Chapter6MIS /> },
  { value: "item-5", title: "بحوث التسويق وخطواتها", icon: Search, component: <Chapter6ResearchProcess /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter6Quiz ? <Quiz questions={chapter6Quiz.questions} chapterId={6} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter6Props {
  sections: typeof chapter6Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter6 = ({ sections, activeSectionValue, isPresentationMode }: Chapter6Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-2xl font-bold text-secondary-blue hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-secondary-blue flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
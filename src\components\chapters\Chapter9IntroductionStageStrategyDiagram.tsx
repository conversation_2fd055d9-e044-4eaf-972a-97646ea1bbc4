import React from 'react';

const StrategyCell = ({ title, className }: { title: string; className?: string }) => (
    <div className={`flex items-center justify-center p-3 text-center text-sm font-medium border border-slate-300 rounded-md h-20 ${className}`}>
        {title}
    </div>
);

export const Chapter9IntroductionStageStrategyDiagram = () => {
  return (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (9-5): استراتيجيات التسعير والترويج في مرحلة التقديم</h4>
        <div className="grid grid-cols-3 grid-rows-3 gap-2 w-full max-w-md mx-auto">
            {/* Top-left empty corner */}
            <div className="col-start-1 row-start-1"></div>
            {/* Promotion Header */}
            <div className="col-span-2 text-center font-bold text-slate-700 text-lg">الترويج</div>
            
            {/* Price Header */}
            <div className="row-span-2 text-center font-bold text-slate-700 text-lg flex items-center justify-center transform rotate-180 [writing-mode:vertical-rl]">السعر</div>

            {/* Grid Cells */}
            <StrategyCell title="عالي" className="col-start-2 row-start-2 bg-blue-100 text-blue-800" />
            <StrategyCell title="آمن" className="col-start-3 row-start-2 bg-blue-100 text-blue-800" />
            
            <StrategyCell title="عالي" className="col-start-1 row-start-2 bg-blue-100 text-blue-800" />
            <StrategyCell title="أعلى الأرباح" className="col-start-2 row-start-2 bg-green-100 text-green-800" />
            <StrategyCell title="تقليل التكاليف التسويقية" className="col-start-3 row-start-2 bg-yellow-100 text-yellow-800" />

            <StrategyCell title="آمن" className="col-start-1 row-start-3 bg-blue-100 text-blue-800" />
            <StrategyCell title="تشجيع المستهلك على الشراء" className="col-start-2 row-start-3 bg-purple-100 text-purple-800" />
            <StrategyCell title="حصة سوقية أكبر" className="col-start-3 row-start-3 bg-red-100 text-red-800" />
        </div>
        <p className="text-center text-xs text-slate-500 mt-6">المصدر: Kotler & Armstrong, Principles of Marketing, 2018, p.248</p>
    </div>
  );
};
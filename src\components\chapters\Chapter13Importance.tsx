import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ArrowRight, ArrowDown, Users, Factory, Store } from 'lucide-react';

const SectionTitle = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div className="pt-6 border-t">
        <h3 className="text-2xl font-bold text-slate-800 mb-4">{title}</h3>
        <div className="space-y-4 text-slate-700 leading-relaxed">{children}</div>
    </div>
);

const SubSectionTitle = ({ number, title, children }: { number: number; title: string; children: React.ReactNode }) => (
    <div className="mt-6">
        <h4 className="font-bold text-xl text-indigo-800 mb-3">{number}. {title}</h4>
        <div className="space-y-3 text-slate-700 leading-relaxed">{children}</div>
    </div>
);

const DirectCommunicationDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (13-1): عملية اتصال مباشرة بين المنتج والمشتري</h4>
        <div className="flex justify-center items-center gap-8">
            <div className="flex flex-col items-center gap-2">
                <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">
                    منتج سلعة - أ
                </div>
                <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">
                    منتج سلعة - ب
                </div>
                <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">
                    منتج سلعة - ج
                </div>
            </div>
            <div className="flex flex-col items-center gap-2">
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
            </div>
            <div className="grid grid-cols-1 gap-2">
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
            </div>
        </div>
        <p className="text-center text-xs text-slate-500 mt-6">Source: Kotler & Armstrong, Principles of Marketing, 2018, p.360</p>
    </div>
);

const IndirectCommunicationDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (13-2): عملية اتصال غير مباشرة بين المنتج والمشتري</h4>
        <div className="flex justify-center items-center gap-8">
            <div className="flex flex-col items-center gap-2">
                <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">
                    منتج سلعة - أ
                </div>
                <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">
                    منتج سلعة - ب
                </div>
                <div className="bg-blue-600 text-white rounded-lg p-3 font-bold text-lg shadow-md">
                    منتج سلعة - ج
                </div>
            </div>
            <div className="flex flex-col items-center gap-2">
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
            </div>
            <div className="flex flex-col items-center gap-2">
                <div className="bg-yellow-500 text-white rounded-full p-4 font-bold text-lg shadow-md w-28 h-28 flex items-center justify-center">
                    وسيط
                </div>
            </div>
            <div className="flex flex-col items-center gap-2">
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
                <ArrowRight className="h-6 w-6 text-slate-500" />
            </div>
            <div className="grid grid-cols-1 gap-2">
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
                <div className="bg-white p-2 rounded-md shadow border text-center font-medium text-slate-700">مشتري</div>
            </div>
        </div>
        <p className="text-center text-xs text-slate-500 mt-6">Source: Kotler & Armstrong, Principles of Marketing, 2018, p.360</p>
    </div>
);

export const Chapter13Importance = () => {
  return (
    <Card className="bg-white shadow-none border-0">
        <CardHeader>
            <CardTitle className="text-3xl font-bold text-slate-800">
                أهمية القناة التسويقية
            </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 text-lg leading-relaxed text-slate-700">
            <p>
                تأسيساً على ما تم الإشارة إليه في مفهوم القناة التسويقية، فإنه يمكننا القول بأن القناة تضيف قيمة سواء كان للسلعة أو الخدمة التي تتعامل بها، أو للمستهلك أو المستعمل المستهدف، أو لذات الوسيط. في القناة لتعزيز مكانته في السوق. وبمعنى يمكن تأشير أهمية المنافذ التسويقية بالآتي:
            </p>

            <SubSectionTitle number={1} title="تحقيق المنفعة (Utility Creation)">
                <p>
                    تحدد القناة التوزيعية ثلاث منافع في وقت مشترك وهي:
                </p>
                <ul className="list-disc pr-6 space-y-2">
                    <li>
                        **الزمانية (Time Utility):** إمتلاك المنتجات في الوقت الذي يرغبه المشتري.
                    </li>
                    <li>
                        **المكانية (Place Utility):** توفير المنتجات في المكان الذي يفضله المشتري.
                    </li>
                    <li>
                        **الحيازية (Possession Utility):** تسهيل عملية نقل ملكية المنتج من البائع إلى المشتري.
                    </li>
                    <li>
                        **الشكلية (Form Utility):** تحويل المواد الخام أو المنتجات نصف المصنعة إلى الشكل النهائي الذي يرغب به المستهلك.
                    </li>
                </ul>
            </SubSectionTitle>

            <SubSectionTitle number={2} title="تقليص عدد عمليات الاتصال">
                <p>
                    تساهم المنافذ التوزيعية غير المباشرة (استخدام الوسيط) في تقليص عدد عمليات الاتصال التي يمكن أن تحصل ما بين المنتج والمشتري بشكل واضح. إذ بدون وجود الوسيط ستجري عملية الاتصال بين الطرفين بشكل مباشر، وهذا ما سيكبد عمل المنتج من جهة، ويزيد الكلف ويضاعف الوقت على المشتري نتيجة تنقله بين المنتجين في حالة حاجته لأكثر من سلعة من جهة أخرى. ويتضح ذلك من خلال الشكلين (13-1) و (13-2) ويبين عدد الاتصالات وحالة الارتباك التي تحصل في عملية الاتصال المباشر ما بين المنتج والمشتري. حيث يفترض في هذا الشكل وجود ثلاثة منتجين، كل منهم متخصص في إنتاج سلعة معينة، وهنالك بالمقابل خمسة مشترين يحتاجون للسلع الثلاث وعليه سيبلغ بهذه الحالة عدد عمليات الاتصال ما بين الطرفين (15) عملية.
                </p>
                <DirectCommunicationDiagram />
                <p>
                    أما بالنسبة للشكل (13-2) وباستخدام الوسيط، ستصبح الحالة معكوسة إذ يتفرغ المنتج لعمله وتتقلص عدد عمليات الاتصال إلى (8) فقط. وبطبيعة الحال إن هذا المثال مبسط وسيزداد التعدد عندما يتزايد عدد المشترين وكذلك عدد المنتجين وهو ما حاصل فعلاً في الحياة العملية.
                </p>
                <IndirectCommunicationDiagram />
            </SubSectionTitle>

            <SubSectionTitle number={3} title="تعميق العلاقة في سلسلة التجهيز">
                <p>
                    تسعى إدارة التوزيع على تحقيق العلاقة المتينة بين العاملين في سلسلة التجهيز لخلق نظام شمولي للتوزيع قادر على خدمة المستهلك وتعزيز الميزة التنافسية للمنظمة. ويتحقق ذلك من خلال المهام التي تقوم بها سلسلة التجهيز وهي:
                </p>
                <ul className="list-disc pr-6 space-y-2">
                    <li>
                        **التخطيط:** تنظيم وتنسيق الأنشطة الداعمة للنشاط التسويقي والتوزيعي.
                    </li>
                    <li>
                        **التوريد:** شراء المواد الضرورية بما يدعم جميع العاملين في سلسلة التجهيز.
                    </li>
                    <li>
                        **التسليم:** تهيئة واستخدام جميع الأنشطة المصممة لتسهيل عمليات إنتقال المنتجات عبر القناة التسويقية وصولاً إلى المستهلك.
                    </li>
                    <li>
                        **بناء العلاقة:** اعتماد الأنشطة التسويقية المرتبطة بتحقيق البيع، الخدمة، لتعميق العلاقة مع المستهلك على الأمد البعيد.
                    </li>
                </ul>
            </SubSectionTitle>

            <SubSectionTitle number={4} title="المعرفة (Knowledge)">
                <p>
                    في الكثير من الأحيان عندما لا يحدث الإتصال ما بين المنتج والمشتري لا يعرف أحدهما ماذا يريد الآخر ولكن عندما يتم إستخدام المنفذ التسويقي سيساعد ذلك في تحقيق التواصل والإمداد بالمعلومات المتبادلة لكل منهما بإتجاه الآخر وذلك عن طريق الإعلان التفاعلي، العلاقات العامة، البيع الشخصي، البحث التسويقي... إلخ.
                </p>
                <p>
                    وبطبيعة الحال فإن تحقيق هذا التواصل والتبادل في المعلومات سيحقق المعرفة لكليهما اتجاه الآخر وبما يسهل من تحقيق أهدافها البيعية أو لإشباع الحاجة أو الرغبة لدى المستهلك.
                </p>
            </SubSectionTitle>
        </CardContent>
    </Card>
  );
};
import React from 'react';
import { ArrowDown, ArrowLeft } from 'lucide-react';

const FlowBox = ({ children, className = '', variant = 'default' }: { children: React.ReactNode; className?: string; variant?: 'default' | 'main' | 'sub' | 'sub-list' }) => {
  const variants = {
    default: 'bg-slate-100 border-slate-300 text-slate-800',
    main: 'bg-indigo-600 border-indigo-800 text-white font-bold text-lg',
    sub: 'bg-indigo-200 border-indigo-400 text-indigo-800 font-semibold text-sm',
    'sub-list': 'bg-indigo-100 border-indigo-300 text-indigo-700 text-xs'
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${variants[variant]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter13Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-indigo-800">
        هيكلية الفصل الثالث عشر
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox variant="main" className="max-w-sm">القنوات التسويقية (التوزيع)</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
          <div className="flex flex-col items-center space-y-2">
            <FlowBox variant="sub">مفهوم وتعريف القناة التسويقية</FlowBox>
            <VerticalArrow />
            <FlowBox variant="sub">أهمية القناة التسويقية</FlowBox>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox variant="sub">القيمة المضافة للقناة التسويقية</FlowBox>
            <VerticalArrow />
            <div className="grid grid-cols-1 gap-2 w-full p-2 bg-indigo-50 rounded-md border-2 border-dashed border-indigo-200">
              <FlowBox variant="sub-list">التملك المادي</FlowBox>
              <FlowBox variant="sub-list">الترويج</FlowBox>
              <FlowBox variant="sub-list">التفاوض</FlowBox>
              <FlowBox variant="sub-list">التمويل</FlowBox>
              <FlowBox variant="sub-list">الخطر</FlowBox>
              <FlowBox variant="sub-list">الطلب</FlowBox>
              <FlowBox variant="sub-list">المعلومات</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox variant="sub">أنواع المنافذ التسويقية</FlowBox>
            <VerticalArrow />
            <div className="grid grid-cols-1 gap-2 w-full p-2 bg-indigo-50 rounded-md border-2 border-dashed border-indigo-200">
              <FlowBox variant="sub-list">سلع المستهلك</FlowBox>
              <FlowBox variant="sub-list">سلع الأعمال</FlowBox>
              <FlowBox variant="sub-list">الخدمات</FlowBox>
            </div>
            <VerticalArrow />
            <FlowBox variant="sub">هيكلة القناة التسويقية</FlowBox>
            <VerticalArrow />
            <div className="grid grid-cols-1 gap-2 w-full p-2 bg-indigo-50 rounded-md border-2 border-dashed border-indigo-200">
              <FlowBox variant="sub-list">التوزيع المحصور</FlowBox>
              <FlowBox variant="sub-list">التوزيع الانتقائي</FlowBox>
              <FlowBox variant="sub-list">التوزيع الشامل</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox variant="sub">السلوك في القناة التسويقية</FlowBox>
            <VerticalArrow />
            <div className="grid grid-cols-1 gap-2 w-full p-2 bg-indigo-50 rounded-md border-2 border-dashed border-indigo-200">
              <FlowBox variant="sub-list">التعاون</FlowBox>
              <FlowBox variant="sub-list">الصراع</FlowBox>
            </div>
          </div>
        </div>

        <div className="flex justify-center items-center my-6 w-full">
            <div className="w-full h-px bg-slate-300 lg:w-1/3"></div>
            <div className="flex-shrink-0 px-2">
                <VerticalArrow />
            </div>
            <div className="w-full h-px bg-slate-300 lg:w-1/3"></div>
        </div>

        <FlowBox variant="main" className="max-w-md">العوامل المؤثرة في اختيار القناة التسويقية</FlowBox>
      </div>
    </div>
  );
};
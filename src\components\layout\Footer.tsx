import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { FileDown, Save, MonitorPlay } from "lucide-react";
import { showSuccess, showError } from "@/utils/toast";
import html2pdf from 'html2pdf.js';
// No longer need useOutletContext here as props are passed directly

interface FooterProps {
  currentChapterTitle: string | null;
  isPresentationMode: boolean; // Add this
  togglePresentationMode: () => void; // Add this
}

export const Footer = ({ currentChapterTitle, isPresentationMode, togglePresentationMode }: FooterProps) => {
  // isPresentationMode and togglePresentationMode are now received as props

  // Note: Progress is static for now.
  const completedChapters = 1;
  const totalChapters = 17;
  const progressPercentage = (completedChapters / totalChapters) * 100;

  const handleExport = () => {
    const contentElement = document.getElementById('chapter-content-area');
    if (contentElement) {
      const fileName = currentChapterTitle ? `${currentChapterTitle}.pdf` : 'exported_chapter.pdf';
      
      // خيارات التصدير لـ html2pdf
      const options = {
        margin: 10,
        filename: fileName,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, useCORS: true }, // useCORS مهم للصور من مصادر خارجية
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      };

      html2pdf().from(contentElement).set(options).save();
      showSuccess(`تم تصدير الفصل "${currentChapterTitle || 'المحدد'}" بصيغة PDF بنجاح!`);
    } else {
      showError("لم يتم العثور على محتوى الفصل للتصدير.");
    }
  };

  return (
    <footer className={`absolute bottom-0 w-full bg-white/90 backdrop-blur-sm border-t ${isPresentationMode ? 'hidden' : ''}`}>
      <div className="container mx-auto px-6 py-3 flex items-center justify-between gap-4">
        <div className="flex items-center gap-4 w-full max-w-xs">
          <span className="text-sm font-medium text-gray-600 whitespace-nowrap">التقدم</span>
          <Progress value={progressPercentage} className="w-full" />
          <span className="text-sm font-bold text-gray-700">{completedChapters}/{totalChapters}</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="hidden sm:flex items-center"
            onClick={togglePresentationMode} // Use the new toggle function
          >
            <MonitorPlay className="ml-2 h-4 w-4" /> {/* New icon */}
            عرض الفصل
          </Button>
          <Button
            variant="outline"
            className="hidden sm:flex items-center"
            onClick={() => showSuccess("تم حفظ تقدمك بنجاح!")}
          >
            <Save className="ml-2 h-4 w-4" />
            حفظ
          </Button>
          <Button
            className="flex items-center"
            onClick={handleExport} // Call the new export handler
          >
            <FileDown className="ml-2 h-4 w-4" />
            تصدير
          </Button>
        </div>
      </div>
    </footer>
  );
};
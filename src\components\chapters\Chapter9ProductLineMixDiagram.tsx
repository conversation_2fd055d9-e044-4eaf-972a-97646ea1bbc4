import React from 'react';

export const ProductMixTable = () => {
  const headers = ["المساهيق", "الأجبان", "الدواجن", "المطلبات", "الغذائية"];
  const rows = [
    ["A-", "A-", "A-", "A-", "A-"],
    ["B-", "B-", "B-", "B-", "B-"],
    ["C-", "C-", "C-", "C-", "C-"],
    ["D-", "D-", "D-", "D-", "D-"],
    ["E-", "E-", "E-", "E-", "E-"],
    ["F-", "F-", "F-", "F-", "F-"],
    ["G-", "G-", "G-", "G-", "G-"],
  ];

  return (
    <div dir="rtl" className="my-8 p-4 bg-slate-50 rounded-xl border overflow-x-auto">
      <h4 className="text-center font-bold text-lg sm:text-xl mb-4 text-slate-800">شكل (2-9): المزيج للمنتج وما يرتبط به من مسميات ذات علاقة</h4>
      <div className="flex flex-col items-center">
        <div className="w-full max-w-2xl">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="border p-2 bg-slate-200 text-slate-700 font-bold text-sm"></th> {/* Empty corner */}
                {headers.map((header, index) => (
                  <th key={index} className="border p-2 bg-slate-200 text-slate-700 font-bold text-sm">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {rows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {rowIndex === 0 && (
                    <td rowSpan={rows.length} className="border p-2 bg-slate-200 text-slate-700 font-bold text-sm text-center align-middle whitespace-nowrap">
                      العمق
                    </td>
                  )}
                  {row.map((cell, cellIndex) => (
                    <td key={cellIndex} className="border p-2 text-center text-slate-700 text-sm">
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
          <div className="text-center mt-4 text-slate-700 font-bold text-sm">
            الاتساع
          </div>
        </div>
      </div>
    </div>
  );
};
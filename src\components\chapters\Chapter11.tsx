import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter11Flowchart } from './Chapter11Flowchart';
import { Chapter11Introduction } from './Chapter11Introduction';
import { Chapter11PromotionMix } from './Chapter11PromotionMix';
import { Chapter11IMC } from './Chapter11IMC';
import { Chapter11CommunicationProcess } from './Chapter11CommunicationProcess';
import { Chapter11Objectives } from './Chapter11Objectives';
import { Chapter11Advertising } from './Chapter11Advertising';
import { Chapter11SalesPromotion } from './Chapter11SalesPromotion';
import { Chapter11PersonalSelling } from './Chapter11PersonalSelling';
import { Chapter11PublicRelations } from './Chapter11PublicRelations';
import { Chapter11DirectMarketing } from './Chapter11DirectMarketing';
import { Chapter11Budgeting } from './Chapter11Budgeting';
import { Chapter11MixStrategy } from './Chapter11MixStrategy';
import { Chapter11SocialResponsibility } from './Chapter11SocialResponsibility';
import { BookOpen, Share2, Blend, Network, MessageCircle, Target, Megaphone, Gift, UserCheck, Handshake, Laptop, DollarSign, Shapes, ShieldCheck, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 11
const chapter11Quiz = quizzes.find(q => q.chapterId === 11);

// Define sections for Chapter 11
export const chapter11Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter11Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter11Introduction /> },
  { value: "item-2", title: "الاتصالات التسويقية المتكاملة (IMC)", icon: Network, component: <Chapter11IMC /> },
  { value: "item-3", title: "عملية الاتصال", icon: MessageCircle, component: <Chapter11CommunicationProcess /> },
  { value: "item-objectives", title: "أهداف الاتصالات التسويقية", icon: Target, component: <Chapter11Objectives /> },
  { value: "item-4", title: "المزيج الترويجي", icon: Blend, component: <Chapter11PromotionMix /> },
  { value: "item-advertising", title: "الإعلان", icon: Megaphone, component: <Chapter11Advertising /> },
  { value: "item-sales-promotion", title: "ترويج المبيعات", icon: Gift, component: <Chapter11SalesPromotion /> },
  { value: "item-personal-selling", title: "البيع الشخصي", icon: UserCheck, component: <Chapter11PersonalSelling /> },
  { value: "item-public-relations", title: "العلاقات العامة", icon: Handshake, component: <Chapter11PublicRelations /> },
  { value: "item-direct-marketing", title: "التسويق الرقمي والمباشر", icon: Laptop, component: <Chapter11DirectMarketing /> },
  { value: "item-budgeting", title: "وضع الميزانية الترويجية", icon: DollarSign, component: <Chapter11Budgeting /> },
  { value: "item-mix-strategy", title: "تشكيل المزيج الترويجي", icon: Shapes, component: <Chapter11MixStrategy /> },
  { value: "item-social-responsibility", title: "المسؤولية الاجتماعية", icon: ShieldCheck, component: <Chapter11SocialResponsibility /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter11Quiz ? <Quiz questions={chapter11Quiz.questions} chapterId={11} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter11Props {
  sections: typeof chapter11Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter11 = ({ sections, activeSectionValue, isPresentationMode }: Chapter11Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-red-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
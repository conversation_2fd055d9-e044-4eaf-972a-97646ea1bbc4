import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter9Flowchart } from './Chapter9Flowchart';
import { Chapter9Introduction } from './Chapter9Introduction';
import { Chapter9Levels } from './Chapter9Levels';
import { Chapter9Classification } from './Chapter9Classification';
import { Chapter9Importance } from './Chapter9Importance';
import { Chapter9ProductManagement } from './Chapter9ProductManagement';
import { Chapter9NewProductDevelopment } from './Chapter9NewProductDevelopment';
import { Chapter9ProductLifeCycle } from './Chapter9ProductLifeCycle';
import { Chapter9Branding } from './Chapter9Branding';
import { Chapter9Packaging } from './Chapter9Packaging';
import { Chapter9Labeling } from './Chapter9Labeling';
import { Chapter9ProductDefinition } from './Chapter9ProductDefinition';
import { Book<PERSON><PERSON>, Share2, Layers, ListTree, Star, PackagePlus, Clock, Award, Box, Ticket, HelpCircle, Blend, Settings, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 9
const chapter9Quiz = quizzes.find(q => q.chapterId === 9);

// Define sections for Chapter 9
export const chapter9Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter9Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter9Introduction /> },
  { value: "item-definition", title: "تعريف المنتج", icon: HelpCircle, component: <Chapter9ProductDefinition /> },
  { value: "item-levels", title: "مستويات الثلاث المنتج والخدمة", icon: Layers, component: <Chapter9Levels /> },
  { value: "item-classification", title: "تصنيفات المنتجات والخدمات", icon: ListTree, component: <Chapter9Classification /> },
  { value: "item-importance", title: "أهمية المنتج", icon: Star, component: <Chapter9Importance /> },
  { value: "item-product-management", title: "قرارات المنتج والمزيج", icon: Settings, component: <Chapter9ProductManagement /> },
  { value: "item-new-product-development", title: "تطوير المنتج الجديد", icon: PackagePlus, component: <Chapter9NewProductDevelopment /> },
  { value: "item-product-life-cycle", title: "دورة حياة المنتج", icon: Clock, component: <Chapter9ProductLifeCycle /> },
  { value: "item-branding", title: "استراتيجية العلامة التجارية", icon: Award, component: <Chapter9Branding /> },
  { value: "item-packaging", title: "التغليف", icon: Box, component: <Chapter9Packaging /> },
  { value: "item-labeling", title: "الترميز والملصقات", icon: Ticket, component: <Chapter9Labeling /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter9Quiz ? <Quiz questions={chapter9Quiz.questions} chapterId={9} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter9Props {
  sections: typeof chapter9Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean; // New prop
}

export const Chapter9 = ({ sections, activeSectionValue, isPresentationMode }: Chapter9Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")} // Only set defaultValue if not in presentation mode
      value={isPresentationMode ? activeSectionValue : undefined} // Control only in presentation mode
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-purple-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
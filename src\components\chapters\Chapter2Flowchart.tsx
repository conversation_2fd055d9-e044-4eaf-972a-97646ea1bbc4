import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '' }: { children: React.ReactNode; className?: string; }) => {
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[50px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 font-semibold ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-500 mx-auto my-2" />;

export const Chapter2Flowchart = () => {
  return (
    <div dir="rtl" className="p-6 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-8 text-indigo-800">
        هيكلية الفصل الثاني
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox className="w-60 text-lg bg-indigo-600 border-indigo-800 text-white">إدارة التسويق</FlowBox>
        <VerticalArrow />
        
        <div className="w-full max-w-4xl grid grid-cols-1 md:grid-cols-3 gap-6">
            <FlowBox className="bg-blue-500 border-blue-700 text-white">تعريف إدارة التسويق</FlowBox>
            
            <div className="flex flex-col items-center space-y-2">
                <FlowBox className="bg-teal-500 border-teal-700 text-white">عمليات إدارة التسويق</FlowBox>
                <VerticalArrow />
                <div className="space-y-2 w-full p-2 bg-teal-50 rounded-md border-2 border-dashed border-teal-200">
                    <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">التحليل</FlowBox>
                    <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">التخطيط</FlowBox>
                    <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">التنفيذ</FlowBox>
                    <FlowBox className="bg-teal-100 border-teal-300 text-teal-800 text-sm">الرقابة</FlowBox>
                </div>
            </div>

            <FlowBox className="bg-cyan-500 border-cyan-700 text-white">واجبات إدارة التسويق</FlowBox>
        </div>

        <VerticalArrow />
        <p className="text-slate-600 text-sm mb-2">(جميع الأقسام ترتبط بالأخلاقيات)</p>
        <FlowBox className="w-72 text-lg bg-rose-500 border-rose-700 text-white">إدارة التسويق والأخلاقيات</FlowBox>
      </div>
    </div>
  );
};
import React from 'react';
import { ArrowLeft } from 'lucide-react';

const ListBox = ({ title, items, className }: { title: string; items: string[]; className?: string }) => (
    <div className={`p-4 border rounded-lg shadow-sm bg-white w-48 ${className}`}>
        <h5 className="font-bold text-center mb-2 pb-2 border-b text-sm">{title}</h5>
        <ul className="space-y-1 text-xs text-slate-600 text-right">
            {items.map((item, index) => (
                <li key={index}>- {item}</li>
            ))}
        </ul>
    </div>
);

export const Chapter11DirectMarketingDiagram = () => {
    const traditionalItems = [
        "البيع المباشر وجهاً لوجه",
        "التسويق عبر البريد المباشر",
        "التسويق عبر الكتالوج",
        "التسويق عبر التلفزيون",
        "التسويق عبر الأكشاك"
    ];

    const digitalItems = [
        "التسويق عبر وسائل التواصل الاجتماعي",
        "التسويق عبر المحركات",
        "التسويق عبر الهواتف"
    ];

    return (
        <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
            <h4 className="text-center font-bold text-xl mb-2 text-slate-800">شكل (11-4): التسويق الرقمي والمباشر</h4>
            <p className="text-center text-xs text-slate-500 mb-6">Source: Kotler & Armstrong, Principles of Marketing, 2018, p.515</p>
            <div className="flex flex-col md:flex-row-reverse items-center justify-center gap-4">
                <ListBox title="التسويق الرقمي" items={digitalItems} />
                <div className="flex flex-col items-center gap-2">
                    <ArrowLeft className="h-6 w-6 text-slate-500 transform -rotate-90 md:rotate-0" />
                    <div className="p-4 border-2 border-dashed border-blue-400 rounded-full bg-blue-100 text-blue-800 text-center font-semibold text-sm w-32 h-32 flex items-center justify-center">
                        بناء علاقة مباشرة مع الزبون
                    </div>
                    <ArrowLeft className="h-6 w-6 text-slate-500 transform rotate-90 md:rotate-180" />
                </div>
                <ListBox title="التسويق المباشر التقليدي" items={traditionalItems} />
            </div>
        </div>
    );
};
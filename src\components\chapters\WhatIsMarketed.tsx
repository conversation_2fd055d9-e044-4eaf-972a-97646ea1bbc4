import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Package,
  Wrench,
  CalendarDays,
  Sparkles,
  User,
  MapPin,
  KeyRound,
  Building2,
  Info,
  type LucideIcon
} from "lucide-react";
import React from "react";

interface MarketedItemProps {
  icon: LucideIcon;
  title: string;
  description: string;
  examples: string;
}

const MarketedItemCard = ({ icon: Icon, title, description, examples }: MarketedItemProps) => (
  <Card className="h-full flex flex-col bg-white shadow-sm hover:shadow-md transition-shadow duration-300">
    <CardHeader className="flex-row items-center gap-3 pb-4">
      <Icon className="h-8 w-8 text-secondary-blue flex-shrink-0" />
      <CardTitle className="text-xl text-dark-gray">{title}</CardTitle>
    </CardHeader>
    <CardContent className="flex-grow">
      <p className="text-text-gray mb-3 leading-relaxed">{description}</p>
      <p className="text-sm text-slate-500 bg-slate-50 p-2 rounded-md"><span className="font-semibold">أمثلة:</span> {examples}</p>
    </CardContent>
  </Card>
);

const marketedItems = [
    {
        icon: Package,
        title: "١. السلع",
        description: "الركيزة الأساسية للإنتاج والتسويق. تشمل مجموعة واسعة من المنتجات الملموسة التي تستهلكها الجماهير.",
        examples: "المنتجات الغذائية، الملابس، السيارات، الأجهزة المنزلية والكهربائية."
    },
    {
        icon: Wrench,
        title: "٢. الخدمات",
        description: "تشكل جزءًا كبيرًا من الاقتصادات المتقدمة، وهي أنشطة أو منافع غير ملموسة تُقدم لإشباع حاجات الأفراد.",
        examples: "شركات الطيران، البنوك، الفنادق، الخدمات الطبية، الهندسة، الاستشارات."
    },
    {
        icon: CalendarDays,
        title: "٣. الأحداث",
        description: "عملية الترويج لأحداث عالمية أو محلية ذات تأثير عام ويتم تسويقها لأطراف مختلفة أو ذات اهتمام بها.",
        examples: "الألعاب الأولمبية، مباريات كأس العالم، المعارض الفنية."
    },
    {
        icon: Sparkles,
        title: "٤. الخبرات",
        description: "إعداد برامج تسويقية لعرض تجارب وخبرات معينة يمكن أن تحقق للمشارك فيها استفادة مستقبلية.",
        examples: "برامج المعايشة، المشاريع ذات العلاقة، معسكرات التدريب للفرق."
    },
    {
        icon: User,
        title: "٥. الأشخاص",
        description: "تسويق الذات أصبح شائعًا، حيث يسوق المشاهير والخبراء أنفسهم للآخرين لتحقيق أهدافهم.",
        examples: "الفنانون، الأطباء، المهندسون، الخبراء الماليون، الرياضيون."
    },
    {
        icon: MapPin,
        title: "٦. الأماكن",
        description: "تسويق المدن والدول والمناطق لجذب السياح والاستثمارات والسكان الجدد.",
        examples: "باريس كعاصمة للموضة، القاهرة كعاصمة للفن، إسطنبول، البتراء."
    },
    {
        icon: KeyRound,
        title: "٧. الممتلكات",
        description: "حقوق الملكية غير المادية التي يمكن حيازتها وبيعها وشراؤها في أسواق متخصصة.",
        examples: "العقارات (أراضٍ ومبانٍ)، الأسهم والسندات."
    },
    {
        icon: Building2,
        title: "٨. المنظمات",
        description: "تسعى الشركات والمنظمات المختلفة إلى تسويق نفسها وبناء صورة إيجابية لدى الجمهور لتحقيق أهدافها.",
        examples: "الجامعات، المتاحف، الفرق المسرحية، النوادي الرياضية، الجمعيات الخيرية."
    },
    {
        icon: Info,
        title: "٩. المعلومات",
        description: "في عالم اليوم، تعتبر المعلومة والمعرفة ميزة تنافسية. يتم إنتاج وتسويق المعلومات كمنتج بحد ذاته.",
        examples: "مكاتب البحوث، دور النشر، المواقع الإلكترونية، المنظمات الدولية."
    }
];

export const WhatIsMarketed = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {marketedItems.map((item) => (
        <MarketedItemCard key={item.title} {...item} />
      ))}
    </div>
  );
};
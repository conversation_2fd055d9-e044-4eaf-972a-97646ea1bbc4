import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { PackagePlus, Rows, SlidersHorizontal, Gem, ArrowRightLeft, Percent, MapPin, Sparkles, Globe, Tag, Package, Briefcase, Wallet, PackageCheck } from 'lucide-react';

const StrategyCard = ({ icon: Icon, title, children, color }: { icon: React.ElementType; title: string; children: React.ReactNode; color: string }) => (
    <Card className={`shadow-md border-t-4 ${color}`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-slate-800">
                <Icon className={`h-8 w-8 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4 text-slate-700 leading-relaxed">
            {children}
        </CardContent>
    </Card>
);

const SubStrategy = ({ icon: Icon, title, children }: { icon: React.ElementType; title: string; children: React.ReactNode }) => (
    <div className="p-4 bg-slate-50 rounded-lg border">
        <h5 className="font-bold text-lg text-slate-700 flex items-center gap-2 mb-2">
            <Icon className="h-5 w-5 text-yellow-600" />
            {title}
        </h5>
        <p className="text-slate-600 leading-relaxed pr-7">{children}</p>
    </div>
);

const productMixStrategies = [
    { icon: Rows, title: "تسعير خط المنتج", description: "تحديد فروق سعرية بين مختلف المنتجات في خط الإنتاج بناءً على فروق التكلفة، تقييمات العملاء للميزات المختلفة، وأسعار المنافسين." },
    { icon: PackagePlus, title: "تسعير المنتج الاختياري", description: "تسعير المنتجات الاختيارية أو الملحقة التي تباع مع المنتج الرئيسي. (مثال: نظام الملاحة في السيارة)." },
    { icon: Package, title: "تسعير المنتج الإلزامي", description: "تحديد سعر للمنتجات التي يجب استخدامها مع المنتج الرئيسي. (مثال: شفرات الحلاقة، خراطيش الطابعة)." },
    { icon: Briefcase, title: "تسعير المنتج الثانوي", description: "تحديد سعر للمنتجات الثانوية (By-products) منخفضة القيمة لجعل سعر المنتج الرئيسي أكثر تنافسية." },
    { icon: PackageCheck, title: "تسعير حزمة المنتج", description: "دمج عدة منتجات وتقديم الحزمة بسعر مخفض. (مثال: وجبات القيمة في مطاعم الوجبات السريعة)." }
];

export const Chapter12PricingStrategies = () => {
  return (
    <div className="space-y-10">
      <StrategyCard icon={Gem} title="1. استراتيجيات تسعير المنتجات الجديدة" color="border-t-purple-500">
        <SubStrategy icon={ArrowRightLeft} title="أ. استراتيجية كشط السوق (Market-Skimming Pricing)">
            تحديد سعر أولي مرتفع للمنتج الجديد "لكشط" أقصى إيرادات طبقة تلو الأخرى من الشرائح الراغبة في دفع السعر المرتفع. تجعل الشركة مبيعات أقل ولكنها أكثر ربحية. هذه الاستراتيجية منطقية فقط في ظل ظروف معينة: يجب أن تدعم جودة المنتج وصورته سعره المرتفع، ويجب أن يكون هناك عدد كافٍ من المشترين.
        </SubStrategy>
        <SubStrategy icon={ArrowRightLeft} title="ب. استراتيجية اختراق السوق (Market-Penetration Pricing)">
            تحديد سعر أولي منخفض للمنتج الجديد لاختراق السوق بسرعة وعمق، وجذب عدد كبير من المشترين بسرعة والفوز بحصة سوقية كبيرة. يجب أن يكون السوق حساسًا للسعر، ويجب أن تنخفض تكاليف الإنتاج والتوزيع مع زيادة حجم المبيعات.
        </SubStrategy>
      </StrategyCard>

      <StrategyCard icon={Rows} title="2. استراتيجيات تسعير مزيج المنتجات" color="border-t-blue-500">
        <p>تطبق الشركات التي لديها مجموعة من المنتجات استراتيجيات تسعير تهدف إلى تعظيم أرباح المزيج الكلي. الجدول (12-1) يوضح الاستراتيجيات الخمس الرئيسية:</p>
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead className="w-1/3 text-right font-bold">الاستراتيجية</TableHead>
                    <TableHead className="text-right font-bold">الوصف</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {productMixStrategies.map((strategy) => (
                    <TableRow key={strategy.title}>
                        <TableCell className="font-semibold flex items-center gap-2"><strategy.icon className="h-5 w-5 text-blue-600" /> {strategy.title}</TableCell>
                        <TableCell>{strategy.description}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
      </StrategyCard>

      <StrategyCard icon={SlidersHorizontal} title="3. استراتيجيات تعديل الأسعار" color="border-t-orange-500">
        <p>تقوم الشركات عادة بتعديل أسعارها الأساسية لمراعاة مختلف العملاء والمواقف المتغيرة. هناك سبع استراتيجيات لتعديل الأسعار:</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SubStrategy icon={Percent} title="التسعير بالخصم والسماحات">تخفيض الأسعار لمكافأة استجابات العملاء مثل الدفع المبكر أو الترويج للمنتج.</SubStrategy>
            <SubStrategy icon={Tag} title="التسعير المجزأ">بيع منتج بسعرين أو أكثر، حيث لا يعتمد الفرق في الأسعار على فروق التكلفة. (مثال: أسعار مختلفة لتذاكر السينما حسب العمر).</SubStrategy>
            <SubStrategy icon={Wallet} title="التسعير النفسي">تحديد الأسعار بناءً على التأثير النفسي للسعر، وليس فقط على الجانب الاقتصادي. (مثال: استخدام سعر 9.99$ بدلاً من 10$).</SubStrategy>
            <SubStrategy icon={Sparkles} title="التسعير الترويجي">تحديد أسعار منخفضة مؤقتًا للمنتجات لزيادة المبيعات على المدى القصير.</SubStrategy>
            <SubStrategy icon={MapPin} title="التسعير الجغرافي">تحديد أسعار مختلفة للعملاء في أجزاء مختلفة من البلاد أو العالم.</SubStrategy>
            <SubStrategy icon={ArrowRightLeft} title="التسعير الديناميكي">تعديل الأسعار باستمرار لتلبية خصائص واحتياجات العملاء والمواقف الفردية.</SubStrategy>
            <SubStrategy icon={Globe} title="التسعير الدولي">تحديد أسعار مختلفة لمختلف البلدان.</SubStrategy>
        </div>
      </StrategyCard>
    </div>
  );
};
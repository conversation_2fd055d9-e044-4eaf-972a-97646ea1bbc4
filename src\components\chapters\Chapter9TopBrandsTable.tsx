import React from 'react';

export const Chapter9TopBrandsTable = () => {
  const data = [
    { rank: 1, brand: "Apple", marketValue: 214, change: "16%" },
    { rank: 2, brand: "Googel", marketValue: 155, change: "10%" },
    { rank: 3, brand: "Amazon", marketValue: 101, change: "56%" },
    { rank: 4, brand: "Microsoft", marketValue: 93, change: "16%" },
    { rank: 5, brand: "Coca Cola", marketValue: 66, change: "-5%" },
    { rank: 6, brand: "Samsung", marketValue: 60, change: "6%" },
    { rank: 7, brand: "Toyota", marketValue: 53, change: "6%" },
    { rank: 8, brand: "Mercedes Benz", marketValue: 49, change: "5%" },
    { rank: 9, brand: "Faecbook", marketValue: 45, change: "6%" },
    { rank: 10, brand: "Mc <PERSON>'s", marketValue: 43, change: "5%" },
  ];

  return (
    <div dir="rtl" className="my-8 p-4 bg-slate-50 rounded-xl border overflow-x-auto">
      <h4 className="text-center font-bold text-lg sm:text-xl mb-4 text-slate-800">جدول (9-1) أفضل عشر علامات في العالم لعام 2018</h4>
      <p className="text-center text-xs text-slate-500 mb-4">Source: www.Irtarabi.com</p>
      <div className="flex justify-center">
        <table className="w-full max-w-2xl border-collapse">
          <thead>
            <tr className="bg-slate-200">
              <th className="border p-2 text-slate-700 font-bold text-sm">ت</th>
              <th className="border p-2 text-slate-700 font-bold text-sm">اسم العلامة التجارية</th>
              <th className="border p-2 text-slate-700 font-bold text-sm">القيمة السوقية مليار $</th>
              <th className="border p-2 text-slate-700 font-bold text-sm">% للتغيير عن السنة السابقة</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-slate-50"}>
                <td className="border p-2 text-center text-slate-700 text-sm">{item.rank}</td>
                <td className="border p-2 text-right text-slate-700 text-sm">{item.brand}</td>
                <td className="border p-2 text-center text-slate-700 text-sm">{item.marketValue}</td>
                <td className="border p-2 text-center text-slate-700 text-sm">{item.change}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
import React from 'react';
import { ArrowDown } from 'lucide-react';

const ProcessStep = ({ children, className }: { children: React.ReactNode, className?: string }) => (
    <div className={`w-full sm:w-56 p-3 border rounded-lg shadow-sm text-center font-semibold ${className}`}>
        {children}
    </div>
);

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-600 my-2" />;

export const Chapter7BuyingProcessDiagram = () => {
  return (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">شكل (5-4): خطوات عملية الشراء لدى المستهلك</h4>
        <div className="flex flex-col items-center relative">
            <ProcessStep className="bg-blue-100 border-blue-300 text-blue-800">إدراك المشكلة</ProcessStep>
            <VerticalArrow />
            <ProcessStep className="bg-green-100 border-green-300 text-green-800">البحث عن المعلومات</ProcessStep>
            <VerticalArrow />
            <ProcessStep className="bg-yellow-100 border-yellow-300 text-yellow-800">تقييم البدائل</ProcessStep>
            <VerticalArrow />
            <ProcessStep className="bg-purple-100 border-purple-300 text-purple-800">اتخاذ قرار الشراء</ProcessStep>
            <VerticalArrow />
            <ProcessStep className="bg-red-100 border-red-300 text-red-800">شعور ما بعد الشراء</ProcessStep>
            <p className="text-xs text-slate-600 mt-4">(توجد تغذية عكسية من مرحلة ما بعد الشراء إلى المراحل السابقة)</p>
        </div>
    </div>
  );
};
import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '' }: { children: React.ReactNode; className?: string; }) => {
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 font-semibold ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-500 mx-auto my-2" />;

export const Chapter4Flowchart = () => {
  return (
    <div dir="rtl" className="p-6 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-8 text-blue-800">
        هيكلية الفصل الرابع
      </h3>
      <div className="flex flex-col items-center">
        
        <FlowBox className="max-w-sm bg-blue-600 border-blue-800 text-white text-lg">تجزئة السوق واستهدافها</FlowBox>
        <VerticalArrow />

        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox className="bg-green-600 border-green-800 text-white">1. تجزئة السوق</FlowBox>
            <VerticalArrow />
            <div className="space-y-2 w-full p-2 bg-green-50 rounded-md border-2 border-dashed border-green-200">
              <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">مفهوم التجزئة</FlowBox>
              <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">أسس التجزئة</FlowBox>
              <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">متطلبات التجزئة</FlowBox>
              <FlowBox className="bg-green-100 border-green-300 text-green-800 text-sm">فوائد التجزئة</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox className="bg-purple-600 border-purple-800 text-white">2. استهداف السوق</FlowBox>
            <VerticalArrow />
            <div className="space-y-2 w-full p-2 bg-purple-50 rounded-md border-2 border-dashed border-purple-200">
              <FlowBox className="bg-purple-100 border-purple-300 text-purple-800 text-sm">تقييم القطاعات</FlowBox>
              <FlowBox className="bg-purple-100 border-purple-300 text-purple-800 text-sm">استراتيجيات الاستهداف</FlowBox>
              <VerticalArrow />
              <div className="space-y-1 w-full p-1 bg-white rounded-md">
                 <FlowBox className="text-xs bg-purple-100 border-purple-300 text-purple-700">غير المتمايزة</FlowBox>
                 <FlowBox className="text-xs bg-purple-100 border-purple-300 text-purple-700">المتمايزة</FlowBox>
                 <FlowBox className="text-xs bg-purple-100 border-purple-300 text-purple-700">المركزة</FlowBox>
              </div>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox className="bg-orange-500 border-orange-700 text-white">3. التموضع</FlowBox>
            <VerticalArrow />
            <div className="space-y-2 w-full p-2 bg-orange-50 rounded-md border-2 border-dashed border-orange-200">
              <FlowBox className="bg-orange-100 border-orange-300 text-orange-800 text-sm">تحديد الميزة التنافسية</FlowBox>
              <FlowBox className="bg-orange-100 border-orange-300 text-orange-800 text-sm">تطوير المزيج التسويقي</FlowBox>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

const Definition = ({ text }: { text: string }) => (
  <div className="p-4 my-4 border-r-4 border-red-600 rounded-lg bg-red-50/50 shadow-sm">
    <blockquote className="text-slate-700 leading-relaxed italic">
      "{text}"
    </blockquote>
  </div>
);

const DefinitionPoint = ({ letter, title, children }: { letter: string; title: string; children: React.ReactNode }) => (
    <div className="p-4 bg-white rounded-lg border border-slate-200 shadow-sm">
        <h5 className="font-bold text-lg text-slate-800">{letter}. {title}</h5>
        <p className="text-slate-600 mt-1 leading-relaxed">{children}</p>
    </div>
);

const advertisingMedia = [
    {
        medium: "الصحف",
        strengths: ["المرونة العالية", "المصداقية", "السرعة في إظهار الإعلان", "التغطية الواسعة للسوق", "التكلفة المنخفضة"],
        weaknesses: ["انخفاض في نوعية الإعلان", "عمر الإعلان قصير", "كثرة التشويش", "ضعف الإخراج الفني للإعلان"]
    },
    {
        medium: "التلفزيون",
        strengths: ["مثير للانتباه وشد الجمهور", "يمزج بين الصوت والصورة واللون", "إمكانية تكرار الإعلان في وقت واحد", "تغطية واسعة للسوق", "متابع للحدث بسرعة"],
        weaknesses: ["تكلفة مرتفعة جداً", "كثير التشويش", "تحتاج إلى خبرة واسعة عند تصميم الإعلان", "يتطلب تواجد الجمهور باستمرار أمام الشاشة", "مرونة منخفضة"]
    },
    {
        medium: "الراديو",
        strengths: ["تغطية جغرافية واسعة", "استخدام واسع من قبل الجمهور", "تكاليف منخفضة", "لا يشترط وجود الجمهور في المنزل", "المرونة العالية"],
        weaknesses: ["يقتصر على الصوت فقط", "قليل الإثارة وشد الانتباه", "احتمالات الضياع في وصول الإعلان للجمهور بسبب انشغالهم بأعمالهم اليومية"]
    },
    {
        medium: "المجلات",
        strengths: ["اختيار متخصص ومتوافق مع طبيعة الإعلان", "تغطية واسعة", "عمر الإعلان طويل", "نوعية عالية"],
        weaknesses: ["التكلفة العالية", "مرونة ضعيفة للتغيير", "فترة الانتظار لإخراج الإعلان طويلة"]
    },
    {
        medium: "البريد المباشر",
        strengths: ["وسيلة شخصية ومباشرة", "مرونة عالية", "غير متنافسة مع وسيلة أخرى", "السرعة العالية في الاتصال", "اختيار محدد"],
        weaknesses: ["تكلف مرتفعة", "ضعف في معدل الاستجابة لدى المستهلك", "التغيير في العناوين"]
    },
    {
        medium: "العرض الخارجي (البوستر)",
        strengths: ["تكلفة قليلة", "مرونة عالية", "منافسة قليلة", "تغطية محددة جغرافياً"],
        weaknesses: ["تأثيرها قليل", "تتعرض للعبث والظروف المناخية", "الإبداع في التصميم قليل"]
    }
];

export const Chapter11Advertising = () => {
  return (
    <div className="space-y-6">
      <p className="text-slate-700 leading-relaxed">
        يحتل الإعلان مكانة متميزة ضمن المزيج الترويجي، بل أن أهميته تصل إلى أن يراه البعض بمكونه الكلمة المرادفة للترويج. نظراً للعمق التاريخي لهذا النشاط قياساً بالأنشطة الترويجية الأخرى ضمن المزيج وقد عرف الإعلان على أنه:
      </p>
      <Definition text="الوسيلة غير الشخصية لتقديم البضائع والخدمات والأفكار بواسطة جهة معلومة ومقابل أجر مدفوع." />
      <p className="text-slate-700 leading-relaxed">
        وهذا التعريف بحقيقته يتكون من أربع أركان رئيسة هي:
      </p>
      <div className="space-y-4">
        <DefinitionPoint letter="أ" title="وسيلة غير شخصية">
          ويعني ذلك بأن الإعلان يصل إلى الجمهور عبر وسائل غير شخصية وسواء كانت مكتوبة أو مشاهدة أو مسموعة أي يتم الإعلان عبر واسطة وليس عن طريق الاتصال الشخصي المباشر.
        </DefinitionPoint>
        <DefinitionPoint letter="ب" title="تقديم البضائع والخدمات والأفكار">
          أي أن الإعلان لا ينحصر في جانب معين من المنتجات بل يشمل السلع المادية والخدمات على اختلاف أشكالها وحتى الأفكار (البرامج التعليمية والتدريبية والاتجاهات الفكرية.. إلخ).
        </DefinitionPoint>
        <DefinitionPoint letter="ج" title="جهة معلومة">
          من شروط الإعلان هو أن يتم من قبل جهة معروفة سواء كانت الجهة المعلنة (صاحبة المنتج) أو الوكالة المتخصصة في الإعلان لأن هذا الشرط يرتبط مع صفات الإعلان وهو المصداقية والالتزام، فإذا ما عرف المصدر فإن ذلك يعني التزامها الكامل بمضمون ما يرد في الإعلان.
        </DefinitionPoint>
        <DefinitionPoint letter="د" title="مقابل أجر مدفوع">
          يتم عرض الإعلان عبر وسائل الاتصال غير الشخصية المختلفة بعد أن يتم دفع مبالغ لقاء ذلك لأن النشاط الإعلاني في جوهره هو نشاط تجاري هادف للربح ويستوجب أن يتم تسديد المبالغ المطلوبة للجهة القائمة بالإعلان. وهذه النقطة تعد الأساس الجوهري في الاختلاف عن الإعلام، إذ أن الأخير يتم عرضه عبر وسائل الاتصال المختلفة ولكن دون دفع أجر مقابل ذلك. وإذا ما أردنا أن نشير إلى أهمية النشاط ودوره الاستراتيجي في الاتصالات التسويقية فيمكن الإشارة إلى أنه أنفق من قبل الشركات في الولايات المتحدة الأمريكية عام 2001 ما يقرب من 250 مليار دولار، وأن الشركات تخصص ما يعادل 2.5% لكل دولار من المبيعات ليتم إنفاقها على الإعلان وأن عدد العاملين.
        </DefinitionPoint>
      </div>

      <div className="pt-8 border-t mt-8">
        <h3 className="text-2xl font-bold text-slate-800 text-center mb-4">جدول (11-1): مكامن القوة والضعف في وسائل الإعلان</h3>
        <Card>
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-1/4 text-right font-bold">الوسيلة</TableHead>
                        <TableHead className="w-1/2 text-right font-bold">مكامن القوة</TableHead>
                        <TableHead className="w-1/2 text-right font-bold">مكامن الضعف</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {advertisingMedia.map((item) => (
                        <TableRow key={item.medium}>
                            <TableCell className="font-semibold">{item.medium}</TableCell>
                            <TableCell>
                                <ul className="list-disc pl-5 space-y-1">
                                    {item.strengths.map((strength, i) => <li key={i}>{strength}</li>)}
                                </ul>
                            </TableCell>
                            <TableCell>
                                <ul className="list-disc pl-5 space-y-1">
                                    {item.weaknesses.map((weakness, i) => <li key={i}>{weakness}</li>)}
                                </ul>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Card>
      </div>
    </div>
  );
};